<?php

use Dompdf\Dompdf;
use Dompdf\Options;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Indukpenduduks $indukpenduduks
 * @property CI_Form_validation $form_validation
 * @property CI_Input $input
 * @property CI_Upload $upload
 */
class IndukPenduduk extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Indukpenduduks', 'indukpenduduks');
        $this->load->library('upload');
        $this->load->library('form_validation');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Get filter parameters
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        $data = array();
        $data['title'] = 'Buku Induk Penduduk';
        $data['content'] = 'master/indukpenduduk/index';
        $data['startdate'] = $startdate;
        $data['enddate'] = $enddate;

        // Build where conditions
        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        // Add date filters
        if ($startdate != null) {
            $where['DATE(a.createddate) >='] = $startdate;
        }
        if ($enddate != null) {
            $where['DATE(a.createddate) <='] = $enddate;
        }

        // Get data with user info
        $data['indukpenduduk'] = $this->indukpenduduks->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->where($where)
            ->order_by('a.createddate', 'DESC')
            ->result();

        return $this->load->view('master', $data);
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Get filter parameters
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        // Add date filters
        if ($startdate != null) {
            $where['DATE(a.createddate) >='] = $startdate;
        }
        if ($enddate != null) {
            $where['DATE(a.createddate) <='] = $enddate;
        }

        // Get data with user info
        $indukpenduduk = $this->indukpenduduks->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->where($where)
            ->order_by('a.createddate', 'DESC')
            ->result();

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/indukpenduduk/export', array(
            'indukpenduduk' => $indukpenduduk,
            'user' => getCurrentUser(),
            'startdate' => $startdate,
            'enddate' => $enddate
        ), true));
        $dompdf->render();
        $dompdf->stream('Buku Induk Penduduk.pdf', array("Attachment" => false));
    }
}
