<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item" aria-current="page">Lembaran & Berita Desa</li>
                </ul>
            </div>
            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Lembaran & Berita Desa</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4>Data Lembaran & Berita Desa</h4>
                </div>

                <div>
                    <?php if (isBPD()): ?>
                        <a href="<?= base_url('master/lembarandesa/add') ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i>
                            <span>Tambah</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <form action="<?= base_url('master/lembarandesa') ?>" method="GET" autocomplete="off">
                    <div class="row align-items-end mb-4">
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label">Jenis</label>
                                <select name="jenis" class="form-control">
                                    <option value="all" <?= $jenis === 'all' ? 'selected' : '' ?>>Semua</option>
                                    <option value="lembaran" <?= $jenis === 'lembaran' ? 'selected' : '' ?>>Lembaran Desa</option>
                                    <option value="berita" <?= $jenis === 'berita' ? 'selected' : '' ?>>Berita Desa</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Mulai</label>
                                <input type="date" name="startdate" class="form-control" value="<?= $startdate ?>" />
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Selesai</label>
                                <input type="date" name="enddate" class="form-control" value="<?= $enddate ?>" />
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">Filter</button>

                                <a href="<?= base_url('master/lembarandesa') ?>" class="btn btn-warning">Reset</a>

                                <button type="button" class="btn btn-danger" onclick="exportData()">Export</button>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <th>No. Urut</th>
                                <th>Jenis</th>
                                <th>Nomor Ditetapkan</th>
                                <th>Tanggal Ditetapkan</th>
                                <th>Tentang</th>
                                <th>Nomor Diundangkan</th>
                                <th>Tanggal Diundangkan</th>
                                <th>Keterangan</th>
                                <th>Dibuat Oleh</th>
                                <?php if (isBPD()): ?>
                                    <th>Action</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($items as $key => $item): ?>
                                <tr>
                                    <td><?= $key + 1 ?></td>
                                    <td>
                                        <?php if ($item->jenis === 'Lembaran Desa'): ?>
                                            <span class="badge badge-primary">Lembaran Desa</span>
                                        <?php elseif ($item->jenis === 'Berita Desa'): ?>
                                            <span class="badge badge-success">Berita Desa</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary"><?= $item->jenis ?: 'Tidak Diketahui' ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $item->nomor_ditetapkan ?: '-' ?></td>
                                    <td><?= $item->tanggal_ditetapkan ? date('d/m/Y', strtotime($item->tanggal_ditetapkan)) : '-' ?></td>
                                    <td><?= $item->tentang ?: '-' ?></td>
                                    <td><?= $item->nomor_diundangkan ?: '-' ?></td>
                                    <td><?= $item->tanggal_diundangkan ? date('d/m/Y', strtotime($item->tanggal_diundangkan)) : '-' ?></td>
                                    <td><?= $item->keterangan ?: '-' ?></td>
                                    <td><?= $item->createdname ?: $item->createdusername ?: '-' ?></td>
                                    <?php if (isBPD()): ?>
                                        <td>
                                            <a href="<?= base_url('master/lembarandesa/edit/' . $item->id) ?>" class="btn btn-sm btn-primary">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteData('<?= base_url('master/lembarandesa/delete/' . $item->id) ?>')">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportData() {
        let jenis = $('select[name="jenis"]').val();
        let startdate = $('input[name="startdate"]').val();
        let enddate = $('input[name="enddate"]').val();

        window.open(`<?= base_url('master/lembarandesa') ?>/export?jenis=${jenis}&startdate=${startdate}&enddate=${enddate}`, '_blank');
    }

    function deleteData(url) {
        if (confirm('Apakah Anda yakin ingin menghapus data ini?')) {
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT === 'SUCCESS') {
                        alert('Data berhasil dihapus');
                        location.reload();
                    } else {
                        alert('Gagal menghapus data: ' + response.MESSAGE);
                    }
                },
                error: function() {
                    alert('Terjadi kesalahan saat menghapus data');
                }
            });
        }
    }
</script>