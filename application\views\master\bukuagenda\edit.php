<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('master/bukuagenda'); ?>">Buku Agenda</a></li>
                    <li class="breadcrumb-item" aria-current="page">Edit Agenda</li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Edit Agenda</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form id="frm" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off" success-redirect="<?= base_url('master/bukuagenda') ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tanggal" class="form-label">Tanggal <span class="text-danger">*</span></label>
                                <input type="date" name="tanggal" id="tanggal" class="form-control" value="<?= $row->tanggal ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jenis" class="form-label">Jenis Agenda <span class="text-danger">*</span></label>
                                <select name="jenis" id="jenis" class="form-control" required onchange="toggleFields()">
                                    <option value="">Pilih Jenis Agenda</option>
                                    <?php foreach ($jenis_options as $key => $value): ?>
                                        <option value="<?= $key ?>" <?= $row->jenis == $key ? 'selected' : '' ?>><?= $value ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nomor_surat" class="form-label">Nomor Surat <span class="text-danger">*</span></label>
                                <input type="text" name="nomor_surat" id="nomor_surat" class="form-control" placeholder="Masukkan Nomor Surat" value="<?= $row->nomor_surat ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6" id="asal_field">
                            <div class="mb-3">
                                <label for="asal" class="form-label">Asal/Instansi Pengirim <span class="text-danger">*</span></label>
                                <input type="text" name="asal" id="asal" class="form-control" placeholder="Masukkan Asal/Instansi Pengirim" value="<?= $row->asal ?>">
                            </div>
                        </div>

                        <div class="col-md-6" id="tujuan_field">
                            <div class="mb-3">
                                <label for="tujuan" class="form-label">Tujuan <span class="text-danger">*</span></label>
                                <input type="text" name="tujuan" id="tujuan" class="form-control" placeholder="Masukkan Tujuan" value="<?= $row->tujuan ?>">
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="perihal" class="form-label">Perihal/Hal dan Isi Singkat <span class="text-danger">*</span></label>
                                <textarea name="perihal" id="perihal" class="form-control" placeholder="Masukkan Perihal/Hal dan Isi Singkat" rows="3" required><?= $row->perihal ?></textarea>
                            </div>
                        </div>

                        <div class="col-md-12" id="keterangan_field">
                            <div class="mb-3">
                                <label for="keterangan" class="form-label">Keterangan/Tembusan</label>
                                <textarea name="keterangan" id="keterangan" class="form-control" placeholder="Masukkan Keterangan/Tembusan" rows="2"><?= $row->keterangan ?></textarea>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="document" class="form-label">Dokumen</label>
                                <input type="file" name="document" id="document" class="form-control" accept=".pdf,.doc,.docx,.xls,.xlsx">
                                <small class="form-text text-muted">Format yang diizinkan: PDF, DOC, DOCX, XLS, XLSX. Kosongkan jika tidak ingin mengubah dokumen.</small>
                                <?php if ($row->document): ?>
                                    <div class="mt-2">
                                        <strong>Dokumen saat ini:</strong>
                                        <a href="<?= base_url('uploads/' . $row->document) ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fa fa-file"></i> Lihat Dokumen
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <a href="<?= base_url('master/bukuagenda') ?>" class="btn btn-danger">Kembali</a>
                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function toggleFields() {
        const jenis = document.getElementById('jenis').value;
        const asalField = document.getElementById('asal_field');
        const tujuanField = document.getElementById('tujuan_field');
        const keteranganField = document.getElementById('keterangan_field');
        const asalInput = document.getElementById('asal');
        const tujuanInput = document.getElementById('tujuan');
        const keteranganLabel = document.querySelector('label[for="keterangan"]');

        // Reset visibility and requirements
        asalField.style.display = 'none';
        tujuanField.style.display = 'none';
        keteranganField.style.display = 'block';
        asalInput.required = false;
        tujuanInput.required = false;

        if (jenis === 'Surat Masuk') {
            asalField.style.display = 'block';
            asalInput.required = true;
            keteranganLabel.innerHTML = 'Keterangan';
        } else if (jenis === 'Surat Keluar') {
            tujuanField.style.display = 'block';
            tujuanInput.required = true;
            keteranganLabel.innerHTML = 'Tembusan';
        }
    }

    // Initialize form on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleFields();
    });
</script>