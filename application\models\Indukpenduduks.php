<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Indukpenduduks extends MY_Model
{
    protected $table = 'indukpenduduk';




    /**
     * Export data to Excel format
     */
    public function exportToArray($where = [])
    {
        $data = $this->result($where);
        $export_data = [];

        foreach ($data as $row) {
            $export_data[] = [
                'Nomor Urut' => $row->nomor_urut ?? $row->id,
                'Nama Lengkap/Panggilan' => $row->nama_lengkap ?? $row->nama ?? '',
                'NIK' => $row->nik ?? '',
                'No. KK' => $row->nomor_kk ?? $row->kk ?? '',
                'Jenis <PERSON>in' => $row->jenis_kelamin ?? '',
                'Tempat Lahir' => $row->tempat_lahir ?? '',
                'Tanggal Lahir' => $row->tanggal_lahir ?? '',
                'Agama' => $row->agama ?? '',
                'Pendidikan <PERSON>' => $row->pendidikan_terakhir ?? $row->pendidikan ?? '',
                'Pekerjaan' => $row->pekerjaan ?? '',
                'Dapat Membaca Huruf' => $row->dapat_membaca_huruf ?? 'Ya',
                'Kewarganegaraan' => $row->kewarganegaraan ?? 'WNI',
                'Alamat Lengkap' => $row->alamat_lengkap ?? $row->alamat ?? '',
                'Kedudukan Dalam Keluarga' => $row->kedudukan_dalam_keluarga ?? '',
                'Keterangan' => $row->keterangan ?? ''
            ];
        }

        return $export_data;
    }

    /**
     * Import data from Excel
     */
    public function importFromArray($data_array, $userid)
    {
        $success = 0;
        $errors = [];

        foreach ($data_array as $index => $row) {
            try {
                // Validate required fields
                if (empty($row['nama']) && empty($row['Nama Lengkap/Panggilan'])) {
                    $errors[] = "Baris " . ($index + 1) . ": Nama wajib diisi";
                    continue;
                }

                // Map column names
                $data = [
                    'nama' => $row['Nama Lengkap/Panggilan'] ?? $row['nama'] ?? '',
                    'nik' => $row['NIK'] ?? $row['nik'] ?? '',
                    'kk' => $row['No. KK'] ?? $row['kk'] ?? '',
                    'tempat_lahir' => $row['Tempat Lahir'] ?? $row['tempat_lahir'] ?? '',
                    'tanggal_lahir' => $row['Tanggal Lahir'] ?? $row['tanggal_lahir'] ?? '',
                    'jenis_kelamin' => $row['Jenis Kelamin'] ?? $row['jenis_kelamin'] ?? '',
                    'agama' => $row['Agama'] ?? $row['agama'] ?? '',
                    'pendidikan' => $row['Pendidikan Terakhir'] ?? $row['pendidikan'] ?? '',
                    'pekerjaan' => $row['Pekerjaan'] ?? $row['pekerjaan'] ?? '',
                    'kewarganegaraan' => $row['Kewarganegaraan'] ?? $row['kewarganegaraan'] ?? '',
                    'alamat' => $row['Alamat Lengkap'] ?? $row['alamat'] ?? '',
                    'keterangan' => $row['Keterangan'] ?? $row['keterangan'] ?? '',
                    'userid' => $userid,
                    'createdby' => getCurrentUser()->id ?? $userid,
                    'createddate' => date('Y-m-d H:i:s')
                ];

                // Convert date format if needed
                if (!empty($data['tanggal_lahir'])) {
                    $date = DateTime::createFromFormat('d/m/Y', $data['tanggal_lahir']);
                    if ($date) {
                        $data['tanggal_lahir'] = $date->format('Y-m-d');
                    }
                }



                // Insert data
                if ($this->insert($data)) {
                    $success++;
                } else {
                    $errors[] = "Baris " . ($index + 1) . ": Gagal menyimpan data";
                }
            } catch (Exception $e) {
                $errors[] = "Baris " . ($index + 1) . ": " . $e->getMessage();
            }
        }

        return [
            'success' => $success,
            'errors' => $errors,
            'total' => count($data_array)
        ];
    }
}
