<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('master/penduduksementara'); ?>">Buku Penduduk Sementara</a></li>
                    <li class="breadcrumb-item" aria-current="page">Import Excel</li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Import Data Penduduk Sementara</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4>Upload File Excel</h4>
            </div>

            <div class="card-body">
                <form id="importForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="file" class="form-label">Pilih File Excel <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                                <div class="form-text">Format yang didukung: .xlsx, .xls (Maksimal 5MB)</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-upload"></i> Import Data
                                    </button>
                                    <a href="<?= base_url('master/penduduksementara') ?>" class="btn btn-secondary">
                                        <i class="fa fa-arrow-left"></i> Kembali
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Format Excel Guide -->
        <div class="card">
            <div class="card-header">
                <h4>Format File Excel</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5><i class="fa fa-info-circle"></i> Petunjuk Format Excel</h5>
                    <p>File Excel harus memiliki kolom sesuai urutan berikut:</p>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama Kolom</th>
                                <th>Keterangan</th>
                                <th>Contoh</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Nomor Urut</td>
                                <td>Opsional, akan auto-generate jika kosong</td>
                                <td>1, 2, 3</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Nama Lengkap</td>
                                <td>Wajib diisi</td>
                                <td>John Doe</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>Jenis Kelamin</td>
                                <td>Laki-laki atau Perempuan</td>
                                <td>Laki-laki</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>Nomor Identitas</td>
                                <td>Harus unik</td>
                                <td>1234567890123456</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>Tempat Lahir</td>
                                <td>Opsional</td>
                                <td>Jakarta</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>Tanggal Lahir</td>
                                <td>Format: YYYY-MM-DD</td>
                                <td>1990-01-15</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>Umur</td>
                                <td>Auto-calculate dari tanggal lahir</td>
                                <td>33</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>Pekerjaan</td>
                                <td>Opsional</td>
                                <td>Pegawai Swasta</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>Kebangsaan</td>
                                <td>Opsional</td>
                                <td>Indonesia</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>Keturunan</td>
                                <td>Opsional</td>
                                <td>Jawa</td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td>Datang Dari</td>
                                <td>Opsional</td>
                                <td>Jakarta Selatan</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>Maksud Tujuan Kedatangan</td>
                                <td>Opsional</td>
                                <td>Bekerja</td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td>Nama Alamat Didatangi</td>
                                <td>Opsional</td>
                                <td>Jl. Merdeka No. 1</td>
                            </tr>
                            <tr>
                                <td>14</td>
                                <td>Datang Tanggal</td>
                                <td>Format: YYYY-MM-DD</td>
                                <td>2023-01-15</td>
                            </tr>
                            <tr>
                                <td>15</td>
                                <td>Pergi Tanggal</td>
                                <td>Format: YYYY-MM-DD, opsional</td>
                                <td>2023-12-31</td>
                            </tr>
                            <tr>
                                <td>16</td>
                                <td>Keterangan</td>
                                <td>Opsional</td>
                                <td>Catatan tambahan</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fa fa-exclamation-triangle"></i> Catatan Penting:</h6>
                    <ul class="mb-0">
                        <li>Baris pertama harus berisi header kolom</li>
                        <li>Nomor Identitas harus unik (tidak boleh duplikasi)</li>
                        <li>Umur akan dihitung otomatis dari tanggal lahir</li>
                        <li>Format tanggal harus YYYY-MM-DD (contoh: 2023-01-15)</li>
                        <li>File maksimal 5MB</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Progress</h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Sedang memproses file Excel...</p>
                <p class="text-muted">Mohon tunggu, jangan tutup halaman ini.</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const importForm = document.getElementById('importForm');
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const fileInput = document.getElementById('file');
        const file = fileInput.files[0];
        
        if (!file) {
            alert('Silakan pilih file Excel terlebih dahulu');
            return;
        }
        
        // Validate file type
        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
        if (!allowedTypes.includes(file.type)) {
            alert('Format file tidak didukung. Gunakan file .xlsx atau .xls');
            return;
        }
        
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('Ukuran file terlalu besar. Maksimal 5MB');
            return;
        }
        
        // Show progress modal
        progressModal.show();
        
        // Create FormData
        const formData = new FormData();
        formData.append('file', file);
        
        // Send AJAX request
        fetch('<?= base_url('master/penduduksementara/process_import') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            progressModal.hide();
            
            if (data.status === 'SUCCESS') {
                alert('Import berhasil: ' + data.message);
                window.location.href = '<?= base_url('master/penduduksementara') ?>';
            } else {
                alert('Import gagal: ' + data.message);
            }
        })
        .catch(error => {
            progressModal.hide();
            console.error('Error:', error);
            alert('Terjadi kesalahan saat import file');
        });
    });
});
</script>
