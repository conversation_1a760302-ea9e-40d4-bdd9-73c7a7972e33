<?php

use Dompdf\Dompdf;
use Dompdf\Options;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Superadmins $admins
 * @property Agendas $agendas
 * @property CI_Upload $upload
 */
class BukuAgenda extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Superadmins', 'admins');
        $this->load->model('Agendas', 'agendas');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        $where = array();
        if ($startdate != null) {
            $where['DATE(a.tanggal) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal) <='] = $enddate;
        }

        $data = array();
        $data['title'] = 'Buku Agenda';
        $data['content'] = 'master/bukuagenda/index';

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();

            $bpdid = array();
            foreach ($bpd as $key => $value) {
                $bpdid[] = $value->id;
            }

            $this->agendas->where_in('userid', $bpdid);
        }

        $data['agendas'] = $this->agendas->order_by('a.nomor_urut', 'ASC')->result($where);

        return $this->load->view('master', $data);
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        $where = array();
        if ($startdate != null) {
            $where['DATE(a.tanggal) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal) <='] = $enddate;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();

            $bpdid = array();
            foreach ($bpd as $key => $value) {
                $bpdid[] = $value->id;
            }

            $this->agendas->where_in('userid', $bpdid);
        }

        $agendas = $this->agendas->order_by('a.nomor_urut', 'ASC')->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/bukuagenda/export', array(
            'agendas' => $agendas,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row()
        ), true));
        $dompdf->render();
        $dompdf->stream('Buku Agenda.pdf', array("Attachment" => false));
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Tambah Agenda';
        $data['content'] = 'master/bukuagenda/add';
        $data['jenis_options'] = $this->agendas->getJenisOptions();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        }

        $tanggal = getPost('tanggal');
        $jenis = getPost('jenis');
        $nomor_surat = getPost('nomor_surat');
        $asal = getPost('asal');
        $tujuan = getPost('tujuan');
        $perihal = getPost('perihal');
        $keterangan = getPost('keterangan');
        $document = $_FILES['document']['name'] ?? null;

        if (empty($tanggal)) {
            return JSONResponseDefault('FAILED', 'Tanggal harus diisi');
        }

        if (empty($jenis)) {
            return JSONResponseDefault('FAILED', 'Jenis agenda harus dipilih');
        }

        if (empty($nomor_surat)) {
            return JSONResponseDefault('FAILED', 'Nomor surat harus diisi');
        }

        if (empty($perihal)) {
            return JSONResponseDefault('FAILED', 'Perihal harus diisi');
        }

        // Validate jenis-specific required fields
        if ($jenis == 'Surat Masuk' && empty($asal)) {
            return JSONResponseDefault('FAILED', 'Asal surat harus diisi untuk Surat Masuk');
        }

        if ($jenis == 'Surat Keluar' && empty($tujuan)) {
            return JSONResponseDefault('FAILED', 'Tujuan surat harus diisi untuk Surat Keluar');
        }

        // Check if nomor surat already exists for this jenis
        if ($this->agendas->isNomorSuratExists($nomor_surat, $jenis)) {
            return JSONResponseDefault('FAILED', 'Nomor surat sudah ada untuk jenis ' . $jenis);
        }

        if ($document == null) {
            return JSONResponseDefault('FAILED', 'Dokumen harus diupload');
        }

        $config = array();
        $config['upload_path'] = './uploads/';
        $config['allowed_types'] = 'pdf|doc|docx|xls|xlsx';
        $config['encrypt_name'] = true;

        $this->load->library('upload', $config);

        if (!$this->upload->do_upload('document')) {
            return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
        }

        $insert = array();
        $insert['tanggal'] = $tanggal;
        $insert['jenis'] = $jenis;
        $insert['nomor_surat'] = $nomor_surat;
        $insert['asal'] = $asal;
        $insert['tujuan'] = $tujuan;
        $insert['perihal'] = $perihal;
        $insert['keterangan'] = $keterangan;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();
        $insert['userid'] = getCurrentIdUser();
        $insert['document'] = $this->upload->data('file_name');
        // nomor_urut akan di-generate otomatis oleh model

        $this->agendas->insert($insert);

        return JSONResponseDefault('OK', 'Data agenda berhasil disimpan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $get = $this->agendas->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/bukuagenda'));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Edit Agenda';
        $data['content'] = 'master/bukuagenda/edit';
        $data['row'] = $row;
        $data['jenis_options'] = $this->agendas->getJenisOptions();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->agendas->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Agenda tidak ditemukan');
        }

        $tanggal = getPost('tanggal');
        $jenis = getPost('jenis');
        $nomor_surat = getPost('nomor_surat');
        $asal = getPost('asal');
        $tujuan = getPost('tujuan');
        $perihal = getPost('perihal');
        $keterangan = getPost('keterangan');
        $document = $_FILES['document']['name'] ?? null;

        if (empty($tanggal)) {
            return JSONResponseDefault('FAILED', 'Tanggal harus diisi');
        }

        if (empty($jenis)) {
            return JSONResponseDefault('FAILED', 'Jenis agenda harus dipilih');
        }

        if (empty($nomor_surat)) {
            return JSONResponseDefault('FAILED', 'Nomor surat harus diisi');
        }

        if (empty($perihal)) {
            return JSONResponseDefault('FAILED', 'Perihal harus diisi');
        }

        // Validate jenis-specific required fields
        if ($jenis == 'Surat Masuk' && empty($asal)) {
            return JSONResponseDefault('FAILED', 'Asal surat harus diisi untuk Surat Masuk');
        }

        if ($jenis == 'Surat Keluar' && empty($tujuan)) {
            return JSONResponseDefault('FAILED', 'Tujuan surat harus diisi untuk Surat Keluar');
        }

        // Check if nomor surat already exists for this jenis (excluding current record)
        if ($this->agendas->isNomorSuratExists($nomor_surat, $jenis, $id)) {
            return JSONResponseDefault('FAILED', 'Nomor surat sudah ada untuk jenis ' . $jenis);
        }

        $update = array();
        $update['tanggal'] = $tanggal;
        $update['jenis'] = $jenis;
        $update['nomor_surat'] = $nomor_surat;
        $update['asal'] = $asal;
        $update['tujuan'] = $tujuan;
        $update['perihal'] = $perihal;
        $update['keterangan'] = $keterangan;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        if ($document != null) {
            $config = array();
            $config['upload_path'] = './uploads/';
            $config['allowed_types'] = 'pdf|doc|docx|xls|xlsx';
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('document')) {
                return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
            }

            $update['document'] = $this->upload->data('file_name');
        }

        $this->agendas->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data agenda berhasil diubah');
    }

    public function delete($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->agendas->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Agenda tidak ditemukan');
        }

        $row = $get->row();

        // Delete the document file if exists
        if ($row->document && file_exists('./uploads/' . $row->document)) {
            unlink('./uploads/' . $row->document);
        }

        $this->agendas->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Data agenda berhasil dihapus');
    }
}
