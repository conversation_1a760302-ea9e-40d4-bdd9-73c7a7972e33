<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        /* Area cetak & margin kertas */
        @page {
            size: A4 landscape;
            margin: 12mm;
        }

        html,
        body {
            margin: 0;
            padding: 0;
        }

        * {
            box-sizing: border-box;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }

        /* Lebar area cetak A4 setelah margin: 210 - (12+12) = 186mm */
        .pdf-container {
            width: 273mm;
            margin: 0 auto;
            margin-top: 12mm;
        }

        img {
            max-width: 100%;
            height: auto;
            display: inline-block;
        }

        table.table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            /* kunci lebar kolom */
            font-family: arial, sans-serif;
        }

        table.table th,
        table.table td {
            border: 0.5px solid #000;
            text-align: left;
            padding: 2px;
            word-wrap: break-word;
            overflow-wrap: anywhere;
            /* potong kata panjang */
            font-size: 6px;
        }

        table.table th {
            text-align: center;
            font-weight: normal;
        }

        /* Header di tengah tanpa mendorong layout */
        table.center {
            margin: 0 auto;
            width: 100%;
            border-collapse: collapse;
        }

        table.center th,
        table.center td {
            border: 0;
            padding: 0;
        }

        hr {
            border: 0;
            border-top: 1px solid #000;
            margin: 8px 0 16px;
        }

        /* Tabel tanda tangan: 3 kolom proporsional yang pas area cetak */
        .sig {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .sig td {
            border: 0;
            padding: 4px 0;
            text-align: center;
        }

        .sig .spacer {
            width: 18.6mm;
        }

        /* ~10% dari 186mm */
        .sig .wide {
            width: calc((100% - 18.6mm)/2);
        }
    </style>
</head>

<body>
    <div class="pdf-container">

        <!-- HEADER -->
        <table class="center" style="text-align:center; margin-bottom: 20px;">
            <tr>
                <th rowspan="4" style="width: 30mm; text-align:right; padding-right:10mm;">
                    <img src="<?= base_url('/assets/img/logo-bpd.png') ?>" alt="logo" style="width: 26mm; height: 26mm;">
                </th>
                <th><b>BADAN PERMUSYAWARATAN DESA</b></th>
            </tr>
            <tr>
                <th><b>(BPD)</b></th>
            </tr>
            <tr>
                <th><b>DESA <?= $user->desaname ?> KECAMATAN <?= $user->kecamatanname ?></b></th>
            </tr>
            <tr>
                <th><b>KABUPATEN <?= $user->kabkotaname ?></b></th>
            </tr>
        </table>

        <hr>

        <div style="text-align:center; margin-bottom: 16px;">
            <p>
                BUKU REKAPITULASI JUMLAH PENDUDUK
                <?php if ($bulan && $tahun): ?>
                    BULAN <?= strtoupper($bulan_nama) ?> TAHUN <?= $tahun ?>
                <?php elseif ($bulan): ?>
                    BULAN <?= strtoupper($bulan_nama) ?>
                <?php elseif ($tahun): ?>
                    TAHUN <?= $tahun ?>
                <?php else: ?>
                    SEMUA DATA
                <?php endif; ?>
                <br>
                DESA <?= $user->desaname ?>
            </p>
        </div>

        <!-- DATA TABLE -->
        <table class="table">
            <!-- Set lebar kolom pakai mm (total = 273mm untuk landscape) -->
            <colgroup>
                <col style="width: 8mm;"> <!-- NOMOR URUT -->
                <col style="width: 15mm;"> <!-- NAMA DUSUN -->
                <col style="width: 8mm;"> <!-- WNA L -->
                <col style="width: 8mm;"> <!-- WNA P -->
                <col style="width: 8mm;"> <!-- WNI L -->
                <col style="width: 8mm;"> <!-- WNI P -->
                <col style="width: 8mm;"> <!-- JLH KK -->
                <col style="width: 8mm;"> <!-- JML ANGGOTA -->
                <col style="width: 8mm;"> <!-- JML JIWA -->
                <col style="width: 8mm;"> <!-- LAHIR WNA L -->
                <col style="width: 8mm;"> <!-- LAHIR WNA P -->
                <col style="width: 8mm;"> <!-- LAHIR WNI L -->
                <col style="width: 8mm;"> <!-- LAHIR WNI P -->
                <col style="width: 8mm;"> <!-- DATANG WNA L -->
                <col style="width: 8mm;"> <!-- DATANG WNA P -->
                <col style="width: 8mm;"> <!-- DATANG WNI L -->
                <col style="width: 8mm;"> <!-- DATANG WNI P -->
                <col style="width: 8mm;"> <!-- MENINGGAL WNA L -->
                <col style="width: 8mm;"> <!-- MENINGGAL WNA P -->
                <col style="width: 8mm;"> <!-- MENINGGAL WNI L -->
                <col style="width: 8mm;"> <!-- MENINGGAL WNI P -->
                <col style="width: 8mm;"> <!-- PINDAH WNA L -->
                <col style="width: 8mm;"> <!-- PINDAH WNA P -->
                <col style="width: 8mm;"> <!-- PINDAH WNI L -->
                <col style="width: 8mm;"> <!-- PINDAH WNI P -->
                <col style="width: 8mm;"> <!-- AKHIR WNA L -->
                <col style="width: 8mm;"> <!-- AKHIR WNA P -->
                <col style="width: 8mm;"> <!-- AKHIR WNI L -->
                <col style="width: 8mm;"> <!-- AKHIR WNI P -->
                <col style="width: 8mm;"> <!-- AKHIR JML KK -->
                <col style="width: 8mm;"> <!-- AKHIR JML ANGGOTA -->
                <col style="width: 8mm;"> <!-- AKHIR JML JIWA -->
                <col style="width: 15mm;"> <!-- KET -->
            </colgroup>

            <!-- Header Level 1 -->
            <tr>
                <th rowspan="4" style="text-align: center;">NOMOR URUT</th>
                <th rowspan="4" style="text-align: center;">NAMA DUSUN/ LINGKUNGAN</th>
                <th colspan="7" style="text-align: center;">JUMLAH PENDUDUK AWAL BULAN</th>
                <th colspan="8" style="text-align: center;">TAMBAHAN BULAN INI</th>
                <th colspan="8" style="text-align: center;">PENGURANGAN BULAN INI</th>
                <th colspan="7" rowspan="2" style="text-align: center;">JML PENDUDUK AKHIR BULAN</th>
                <th rowspan="4" style="text-align: center;">KET</th>
            </tr>

            <tr>
                <td colspan="2" style="text-align: center;">WNA</td>
                <td colspan="2" style="text-align: center;">WNI</td>
                <td rowspan="3" style="text-align: center;">JLH KK</td>
                <td rowspan="3" style="text-align: center;">JML ANGGOTA KELUARGA</td>
                <td rowspan="3" style="text-align: center;">JML JIWA</td>
                <td colspan="4" style="text-align: center;">LAHIR</td>
                <td colspan="4" style="text-align: center;">DATANG</td>
                <td colspan="4" style="text-align: center;">MENINGGAL</td>
                <td colspan="4" style="text-align: center;">PINDAH</td>
            </tr>

            <tr>
                <td rowspan="2" style="text-align: center;">L</td>
                <td rowspan="2" style="text-align: center;">P</td>
                <td rowspan="2" style="text-align: center;">L</td>
                <td rowspan="2" style="text-align: center;">P</td>
                <td colspan="2" style="text-align: center;">WNA</td>
                <td colspan="2" style="text-align: center;">WNI</td>
                <td colspan="2" style="text-align: center;">WNA</td>
                <td colspan="2" style="text-align: center;">WNI</td>
                <td colspan="2" style="text-align: center;">WNA</td>
                <td colspan="2" style="text-align: center;">WNI</td>
                <td colspan="2" style="text-align: center;">WNA</td>
                <td colspan="2" style="text-align: center;">WNI</td>
                <td colspan="2" style="text-align: center;">WNA</td>
                <td colspan="2" style="text-align: center;">WNI</td>
                <td rowspan="2" style="text-align: center;">JML KK</td>
                <td rowspan="2" style="text-align: center;">JML ANGGOTA KELUARGA</td>
                <td rowspan="2" style="text-align: center;">JML JIWA</td>
            </tr>

            <tr>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
                <td style="text-align: center;">L</td>
                <td style="text-align: center;">P</td>
            </tr>

            <?php foreach ($rekap as $key => $value): ?>
                <tr>
                    <td style="text-align: center;"><?= $value->nomor_urut ?? ($key + 1) ?></td>
                    <td><?= $value->nama_dusun ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->awal_wna_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->awal_wna_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->awal_wni_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->awal_wni_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->awal_kk ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->awal_anggota_keluarga ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->awal_jiwa ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->tambah_lahir_wna_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->tambah_lahir_wna_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->tambah_lahir_wni_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->tambah_lahir_wni_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->tambah_datang_wna_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->tambah_datang_wna_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->tambah_datang_wni_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->tambah_datang_wni_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kurang_meninggal_wna_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kurang_meninggal_wna_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kurang_meninggal_wni_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kurang_meninggal_wni_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kurang_pindah_wna_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kurang_pindah_wna_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kurang_pindah_wni_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kurang_pindah_wni_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->akhir_wna_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->akhir_wna_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->akhir_wni_l ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->akhir_wni_p ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->akhir_kk ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->akhir_anggota_keluarga ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->akhir_jiwa ?? '-' ?></td>
                    <td><?= $value->keterangan ?? '-' ?></td>
                </tr>
            <?php endforeach; ?>
        </table>

        <!-- TANDA TANGAN -->
        <div style="margin-top: 28px;">
            <table class="sig">
                <tr>
                    <td class="wide">Mengetahui,</td>
                    <td class="spacer"></td>
                    <td class="wide"><?= $user->desaname ?>, <?= tgl_indo(getCurrentDate('Y-m-d')) ?></td>
                </tr>
                <tr>
                    <td class="wide">Ketua BPD</td>
                    <td class="spacer"></td>
                    <td class="wide">Sekertaris BPD</td>
                </tr>
            </table>

            <div style="height: 40mm;"></div>

            <table class="sig">
                <tr>
                    <td class="wide">(_______________)</td>
                    <td class="spacer"></td>
                    <td class="wide">(_______________)</td>
                </tr>
            </table>
        </div>

    </div>
</body>

</html>