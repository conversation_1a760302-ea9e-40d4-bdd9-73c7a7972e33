<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item" aria-current="page">Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga</li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4>Data Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga</h4>
                </div>

                <div>
                    <?php if (isBPD()): ?>
                        <a href="<?= base_url('master/ktpkk/add') ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i>
                            <span>Tambah</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="" class="form-label">Tahun</label>
                                <select name="tahun" class="form-control">
                                    <option value="">Pilih Tahun</option>
                                    <?php for ($i = date('Y'); $i >= 2020; $i--): ?>
                                        <option value="<?= $i ?>" <?= ($tahun == $i) ? 'selected' : '' ?>><?= $i ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="<?= base_url(uri_string()) ?>" class="btn btn-warning">Reset</a>
                                <button type="button" class="btn btn-danger" onclick="exportData()">Export</button>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <th>No. Urut</th>
                                <th>No. KK</th>
                                <th>Nama Lengkap</th>
                                <th>NIK</th>
                                <th>Jenis Kelamin</th>
                                <th>Tempat Lahir</th>
                                <th>Tanggal Lahir</th>
                                <th>Agama</th>
                                <th>Status Perkawinan</th>
                                <th>Status Hubungan Keluarga</th>
                                <th>Tanggal Dikeluarkan</th>
                                <th>Dibuat Oleh</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ktpkk as $key => $value): ?>
                                <tr>
                                    <td><?= $value->nomor_urut ?? ($key + 1) ?></td>
                                    <td><?= $value->no_kk ?? '-' ?></td>
                                    <td><?= $value->nama_lengkap ?? '-' ?></td>
                                    <td><?= $value->nik ?? '-' ?></td>
                                    <td>
                                        <?php if ($value->jenis_kelamin == 'Laki-laki'): ?>
                                            <span class="badge badge-primary">L</span>
                                        <?php elseif ($value->jenis_kelamin == 'Perempuan'): ?>
                                            <span class="badge badge-danger">P</span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $value->tempat_lahir ?? '-' ?></td>
                                    <td>
                                        <?php if ($value->tanggal_lahir): ?>
                                            <?= tgl_indo(date('Y-m-d', strtotime($value->tanggal_lahir))) ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $value->agama ?? '-' ?></td>
                                    <td><?= $value->status_perkawinan ?? '-' ?></td>
                                    <td><?= $value->status_hubungan_keluarga ?? '-' ?></td>
                                    <td>
                                        <?php if ($value->tanggal_dikeluarkan): ?>
                                            <?= tgl_indo(date('Y-m-d', strtotime($value->tanggal_dikeluarkan))) ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td><?= ($value->createdname ?? '') . ' - ' . ($value->createdusername ?? '') ?></td>
                                    <td>
                                        <?php if (isBPD()): ?>
                                            <a href="<?= base_url('master/ktpkk/edit/' . $value->id) ?>" class="btn btn-primary btn-sm" title="Edit">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteData('<?= $value->nama_lengkap ?>', <?= $value->id ?>)" title="Hapus">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportData() {
        let tahun = $('select[name="tahun"]').val();

        window.open(`<?= base_url(uri_string()) ?>/export?tahun=${tahun}`, '_blank');
    }

    function deleteData(name, id) {
        swal({
            title: "Konfirmasi Hapus",
            text: `Apakah Anda yakin ingin menghapus data ${name}?`,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Ya, Hapus!",
            cancelButtonText: "Batal",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: `<?= base_url('master/ktpkk/delete/') ?>${id}`,
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT == 'SUCCESS') {
                        swal("Berhasil!", response.MESSAGE, "success");
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        swal("Gagal!", response.MESSAGE, "error");
                    }
                },
                error: function() {
                    swal("Error!", "Terjadi kesalahan sistem", "error");
                }
            });
        });
    }
</script>
