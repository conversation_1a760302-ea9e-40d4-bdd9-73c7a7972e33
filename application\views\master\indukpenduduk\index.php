<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item" aria-current="page">Buku Induk Penduduk</li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Buku Induk Penduduk</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4>Data Buku Induk Penduduk</h4>
                </div>

                <div>
                    <?php if (isBPD()): ?>
                        <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i>
                            <span>Tambah</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="" class="form-label">Tanggal Mulai</label>
                                <input type="date" name="startdate" class="form-control" value="<?= $startdate ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="" class="form-label">Tanggal Selesai</label>
                                <input type="date" name="enddate" class="form-control" value="<?= $enddate ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="<?= base_url(uri_string()) ?>" class="btn btn-warning">Reset</a>
                                <button type="button" class="btn btn-danger" onclick="exportData()">Export</button>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <th>No. Urut</th>
                                <th>NIK</th>
                                <th>Nama</th>
                                <th>L/P</th>
                                <th>Tempat, Tgl Lahir</th>
                                <th>Agama</th>
                                <th>Pendidikan</th>
                                <th>Pekerjaan</th>
                                <th>Alamat</th>
                                <th>Dibuat Oleh</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($indukpenduduk as $key => $value): ?>
                                <tr>
                                    <td><?= $key + 1 ?></td>
                                    <td><?= $value->nik ?? '-' ?></td>
                                    <td><?= $value->nama_lengkap ?? '-' ?></td>
                                    <td>
                                        <?php if ($value->jenis_kelamin == 'Laki-laki'): ?>
                                            <span class="badge badge-primary">Laki-laki</span>
                                        <?php elseif ($value->jenis_kelamin == 'Perempuan'): ?>
                                            <span class="badge badge-danger">Perempuan</span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $tempat_lahir = $value->tempat_lahir ?? '';
                                        $tanggal_lahir = $value->tanggal_lahir ? tgl_indo(date('Y-m-d', strtotime($value->tanggal_lahir))) : '';
                                        echo $tempat_lahir . ($tempat_lahir && $tanggal_lahir ? ', ' : '') . $tanggal_lahir;
                                        ?>
                                    </td>
                                    <td><?= $value->agama ?? '-' ?></td>
                                    <td><?= $value->pendidikan_terakhir ?? '-' ?></td>
                                    <td><?= $value->pekerjaan ?? '-' ?></td>
                                    <td><?= $value->alamat_lengkap ?? '-' ?></td>
                                    <td><?= ($value->createdname ?? '') . ' - ' . ($value->createdusername ?? '') ?></td>
                                    <td>
                                        <?php if (isBPD()): ?>
                                            <a href="<?= base_url(uri_string() . '/edit/' . $value->id) ?>" class="btn btn-primary btn-sm" title="Edit">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteData('<?= $value->nama_lengkap ?>', <?= $value->id ?>)" title="Hapus">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportData() {
        let startdate = $('input[name="startdate"]').val();
        let enddate = $('input[name="enddate"]').val();

        window.open(`<?= base_url(uri_string()) ?>/export?startdate=${startdate}&enddate=${enddate}`, '_blank');
    }

    function deleteData(name, id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: `Apakah Anda yakin ingin menghapus data ${name}?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `<?= base_url(uri_string() . '/delete/') ?>${id}`,
                    type: 'POST',

                    dataType: 'json',
                    success: function(response) {
                        if (response.RESULT == 'SUCCESS') {
                            Swal.fire('Berhasil!', response.MESSAGE, 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            Swal.fire('Gagal!', response.MESSAGE, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Terjadi kesalahan sistem', 'error');
                    }
                });
            }
        });
    }
</script>