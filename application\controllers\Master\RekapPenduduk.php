<?php

use Dompdf\Dompdf;
use Dompdf\Options;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property RekapPenduduks $rekappenduduks
 * @property Superadmins $admins
 * @property CI_Form_validation $form_validation
 * @property CI_Input $input
 * @property CI_Upload $upload
 * @property CI_Session $session
 */
class RekapPenduduk extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('RekapPenduduks', 'rekappenduduks');
        $this->load->model('Superadmins', 'admins');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $bulan = getGet('bulan');
        $tahun = getGet('tahun');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($bulan != null && $tahun != null) {
            $where['MONTH(a.createddate)'] = $bulan;
            $where['YEAR(a.createddate)'] = $tahun;
        } elseif ($bulan != null) {
            $where['MONTH(a.createddate)'] = $bulan;
        } elseif ($tahun != null) {
            $where['YEAR(a.createddate)'] = $tahun;
        }

        $data = array();
        $data['title'] = 'Rekapitulasi Jumlah Penduduk';
        $data['content'] = 'master/rekappenduduk/index';
        $data['bulan'] = $bulan;
        $data['tahun'] = $tahun;

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $data['rekappenduduk'] = $this->rekappenduduks->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->where($where)
            ->order_by('a.periode', 'DESC')
            ->order_by('a.nomor_urut', 'ASC')
            ->result();

        return $this->load->view('master', $data);
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Get filter parameters
        $bulan = getGet('bulan');
        $tahun = getGet('tahun');

        $where = array();

        if ($bulan != null && $tahun != null) {
            $where['MONTH(a.createddate)'] = $bulan;
            $where['YEAR(a.createddate)'] = $tahun;
        } elseif ($bulan != null) {
            $where['MONTH(a.createddate)'] = $bulan;
        } elseif ($tahun != null) {
            $where['YEAR(a.createddate)'] = $tahun;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $rekap = $this->rekappenduduks->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->where($where)
            ->order_by('a.periode', 'DESC')
            ->order_by('a.nomor_urut', 'ASC')
            ->result();

        // Format bulan dan tahun untuk display
        $bulan_nama = '';
        if ($bulan) {
            $bulan_names = [
                '01' => 'Januari',
                '02' => 'Februari',
                '03' => 'Maret',
                '04' => 'April',
                '05' => 'Mei',
                '06' => 'Juni',
                '07' => 'Juli',
                '08' => 'Agustus',
                '09' => 'September',
                '10' => 'Oktober',
                '11' => 'November',
                '12' => 'Desember'
            ];
            $bulan_nama = $bulan_names[$bulan] ?? '';
        }

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/rekappenduduk/export', array(
            'rekap' => $rekap,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $this->rekappenduduks->getStatistics(getCurrentUser()->id ?? null),
            'bulan' => $bulan,
            'tahun' => $tahun,
            'bulan_nama' => $bulan_nama
        ), true));
        $dompdf->render();
        $dompdf->stream('Buku Rekapitulasi Jumlah Penduduk.pdf', array("Attachment" => false));
    }
}
