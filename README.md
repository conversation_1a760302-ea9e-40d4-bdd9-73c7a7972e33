# Sistem Administrasi Penduduk Terintegrasi

Pengembangan SIAPKADA dengan fokus pada administrasi kependudukan yang terintegrasi untuk mengurangi redundansi input data dan memastikan konsistensi data di seluruh sistem.

## 🚀 Fitur Utama

### ✨ Master Data Penduduk
- **Sumber Data Tunggal**: Semua data penduduk tersentralisasi
- **Import/Export Excel**: Bulk data management dengan template standar
- **Auto-Validation**: Validasi NIK dan data otomatis
- **Advanced Search**: Pencarian dan filter yang powerful

### 🔄 Integrasi Antar Modul
- **Auto-Populate**: Input NIK otomatis mengisi form dari Master Data
- **Real-time Sync**: Sinkronisasi data antar modul secara otomatis
- **Consistency Check**: Deteksi dan perbaikan inkonsistensi data
- **Bulk Operations**: Operasi massal untuk efisiensi

### 📊 Laporan Permendagri 46
- **Format Standar**: Sesuai Permendagri No. 46 Tahun 2016
- **PDF Export**: Cetak laporan profesional
- **Preview Mode**: Preview sebelum cetak
- **Flexible Filtering**: Filter berdasarkan berbagai kriteria

### 📱 User Experience
- **Responsive Design**: Mobile-friendly interface
- **Real-time Validation**: Validasi form secara real-time
- **Quick Actions**: Dashboard dengan aksi cepat
- **Smart Search**: Pencarian NIK dengan modal popup

## 🏗️ Arsitektur Sistem

```
📁 Controllers/
├── 📁 Master/
│   ├── 📄 MasterDataPenduduk.php     # Master data penduduk
│   ├── 📄 IndukPenduduk.php          # Buku induk (updated)
│   ├── 📄 BukuTanahDesa.php          # Administrasi tanah
│   └── 📄 BukuInventarisDesa.php     # Inventaris desa
├── 📁 Integration/
│   └── 📄 DataSync.php               # Sinkronisasi data
└── 📁 Laporan/
    └── 📄 Permendagri46.php          # Laporan standar

📁 Models/
├── 📄 BukuTanahDesas.php             # Model tanah desa
└── 📄 Indukpenduduks.php             # Model induk penduduk

📁 Views/
├── 📁 master/masterdatapenduduk/     # Views master data
├── 📁 integration/datasync/          # Views sinkronisasi
├── 📁 laporan/permendagri46/         # Views laporan
└── 📁 components/                    # Reusable components
    └── 📄 nik_search_widget.php      # Widget pencarian NIK
```

## 🛠️ Instalasi

### Prerequisites
- PHP 7.4+
- MySQL 5.7+
- CodeIgniter 3.x
- Composer (untuk dependencies)

### Setup Database
```sql
-- Import patch database
source sql/patch_update_administrasi_penduduk.sql;
```

### Install Dependencies
```bash
composer install
```

### Configuration
```php
// application/config/database.php
$db['default']['hostname'] = 'localhost';
$db['default']['username'] = 'your_username';
$db['default']['password'] = 'your_password';
$db['default']['database'] = 'your_database';
```

### File Permissions
```bash
chmod 755 uploads/
chmod 755 uploads/temp/
```

## 📋 Modul yang Terintegrasi

| Modul | Status | Fitur Integrasi |
|-------|--------|-----------------|
| 🏠 Master Data Penduduk | ✅ Baru | Sumber data utama |
| 📖 Buku Induk Penduduk | ✅ Updated | Auto-populate dari Master Data |
| 🔄 Buku Mutasi Penduduk | ✅ Updated | Integrasi data personal |
| ⏰ Buku Penduduk Sementara | ✅ Updated | Integrasi data personal |
| 🆔 Buku KTP | ✅ Updated | Auto-populate lengkap |
| 👨‍👩‍👧‍👦 Buku KK | ✅ Updated | Integrasi data keluarga |
| 🏞️ Buku Tanah Desa | ✅ Baru | Administrasi tanah |
| 📦 Buku Inventaris Desa | ✅ Baru | Inventaris barang |
| 📅 Buku Agenda Kegiatan | ✅ Baru | Agenda kegiatan |

## 🎯 Quick Start

### 1. Tambah Data Penduduk
```
Dashboard → Aksi Cepat → Tambah Penduduk
atau
Menu → Master Data Penduduk → Tambah Data
```

### 2. Import Data Excel
```
Master Data Penduduk → Import Excel → Download Template → Upload File
```

### 3. Sinkronisasi Data
```
Menu → Integrasi → Sinkronisasi Data → Pilih metode sinkronisasi
```

### 4. Cetak Laporan
```
Menu → Laporan → Laporan Permendagri 46 → Pilih jenis laporan
```

## 🔧 Fitur Unggulan

### Auto-Populate Form
Ketika memasukkan NIK di form manapun:
1. ✅ Sistem otomatis mencari di Master Data
2. ✅ Form terisi otomatis jika data ditemukan
3. ✅ Link untuk menambah data jika tidak ditemukan

### Smart Import Excel
1. ✅ Template Excel dengan validasi
2. ✅ Auto-populate dari Master Data saat import
3. ✅ Laporan detail hasil import
4. ✅ Error handling yang informatif

### Consistency Management
1. ✅ Deteksi otomatis inkonsistensi data
2. ✅ Laporan detail masalah yang ditemukan
3. ✅ Perbaikan otomatis dengan satu klik
4. ✅ Monitoring status integrasi real-time

## 📊 Dashboard Features

- **📈 Real-time Statistics**: Statistik penduduk terkini
- **⚡ Quick Actions**: 6 aksi cepat yang sering digunakan
- **🔍 Smart Search**: Pencarian NIK dengan modal popup
- **📋 Recent Activities**: Log aktivitas terbaru
- **🔗 Integration Status**: Status sinkronisasi antar modul
- **📊 Visual Charts**: Grafik distribusi penduduk

## 🎨 User Interface

### Responsive Design
- ✅ Mobile-friendly
- ✅ Touch-optimized
- ✅ Adaptive layout
- ✅ Fast loading

### User Experience
- ✅ Real-time validation
- ✅ Progress indicators
- ✅ Informative error messages
- ✅ Contextual help

## 📖 Dokumentasi

- 📄 [Dokumentasi Lengkap](DOKUMENTASI_SISTEM.md)
- 🔧 [API Documentation](docs/api.md)
- 🎓 [User Manual](docs/user-manual.md)
- 🐛 [Troubleshooting](docs/troubleshooting.md)

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📝 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Chat: [WhatsApp Support](https://wa.me/6281234567890)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)

## 🙏 Acknowledgments

- Tim Pengembang SIAPKADA
- Kementerian Dalam Negeri RI
- Komunitas CodeIgniter Indonesia

---

**Made with ❤️ for Indonesian Villages**
