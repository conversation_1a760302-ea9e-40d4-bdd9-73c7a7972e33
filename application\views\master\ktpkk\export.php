<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penduduk Sementara</title>
    <style>
        /* Area cetak & margin kertas */
        @page {
            size: A4 landscape;
            margin: 12mm;
        }

        html,
        body {
            margin: 0;
            padding: 0;
        }

        * {
            box-sizing: border-box;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }

        /* Lebar area cetak A4 setelah margin: 210 - (12+12) = 186mm */
        .pdf-container {
            width: 273mm;
            margin: 0 auto;
            margin-top: 12mm;
        }

        img {
            max-width: 100%;
            height: auto;
            display: inline-block;
        }

        table.table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            /* kunci lebar kolom */
            font-family: arial, sans-serif;
        }

        table.table th,
        table.table td {
            border: 0.5px solid #000;
            text-align: left;
            padding: 8px;
            word-wrap: break-word;
            overflow-wrap: anywhere;
            /* potong kata panjang */
            font-size: 12px;
        }

        table.table th {
            text-align: center;
            font-weight: normal;
        }

        /* Header di tengah tanpa mendorong layout */
        table.center {
            margin: 0 auto;
            width: 100%;
            border-collapse: collapse;
        }

        table.center th,
        table.center td {
            border: 0;
            padding: 0;
        }

        hr {
            border: 0;
            border-top: 1px solid #000;
            margin: 8px 0 16px;
        }

        /* Tabel tanda tangan: 3 kolom proporsional yang pas area cetak */
        .sig {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .sig td {
            border: 0;
            padding: 4px 0;
            text-align: center;
        }

        .sig .spacer {
            width: 18.6mm;
        }

        /* ~10% dari 186mm */
        .sig .wide {
            width: calc((100% - 18.6mm)/2);
        }
    </style>
</head>

<body>
    <div class="pdf-container">

        <!-- HEADER -->
        <table class="center" style="text-align:center; margin-bottom: 20px;">
            <tr>
                <th rowspan="4" style="width: 30mm; text-align:right; padding-right:10mm;">
                    <img src="<?= base_url('/assets/img/logo-bpd.png') ?>" alt="logo" style="width: 26mm; height: 26mm;">
                </th>
                <th><b>PEMERINTAH DESA</b></th>
            </tr>
            <tr>
                <th><b>DESA <?= $user->desaname ?> KECAMATAN <?= $user->kecamatanname ?></b></th>
            </tr>
            <tr>
                <th><b>KABUPATEN <?= $user->kabkotaname ?></b></th>
            </tr>
            <tr>
                <th><b>PROVINSI</b></th>
            </tr>
        </table>

        <hr>

        <div style="text-align:center; margin-bottom: 16px;">
            <p>
                BUKU PENDUDUK SEMENTARA<br>
                DESA <?= $user->desaname ?>
            </p>
        </div>

        <!-- DATA TABLE -->
        <table class="table">
            <!-- Set lebar kolom pakai mm (total = 273mm untuk landscape) -->
            <colgroup>
                <col style="width: 12mm;"> <!-- No. Urut -->
                <col style="width: 25mm;"> <!-- Nama Lengkap -->
                <col style="width: 8mm;"> <!-- L -->
                <col style="width: 8mm;"> <!-- P -->
                <col style="width: 20mm;"> <!-- Nomor Identitas -->
                <col style="width: 25mm;"> <!-- Tempat dan Tanggal Lahir/Umur -->
                <col style="width: 18mm;"> <!-- Pekerjaan -->
                <col style="width: 15mm;"> <!-- Kebangsaan -->
                <col style="width: 15mm;"> <!-- Keturunan -->
                <col style="width: 20mm;"> <!-- Datang Dari -->
                <col style="width: 25mm;"> <!-- Maksud dan Tujuan Kedatangan -->
                <col style="width: 25mm;"> <!-- Nama dan Alamat Yang Didatangi -->
                <col style="width: 15mm;"> <!-- Datang Tanggal -->
                <col style="width: 15mm;"> <!-- Pergi Tanggal -->
                <col style="width: 15mm;"> <!-- Keterangan -->
            </colgroup>

            <!-- Header dengan rowspan untuk jenis kelamin dan kewarganegaraan -->
            <tr>
                <th rowspan="2">NOMOR URUT</th>
                <th rowspan="2">NAMA LENGKAP</th>
                <th colspan="2">JENIS KELAMIN</th>
                <th rowspan="2">NOMOR IDENTITAS/ TANDA PENGENAL</th>
                <th rowspan="2">TEMPAT DAN TANGGAL LAHIR/ UMUR</th>
                <th rowspan="2">PEKERJAAN</th>
                <th colspan="2">KEWARGANEGARAAN</th>
                <th rowspan="2">DATANG DARI</th>
                <th rowspan="2">MAKSUD DAN TUJUAN KEDATANGAN</th>
                <th rowspan="2">NAMA DAN ALAMAT YG DIDATANGI</th>
                <th rowspan="2">DATANG TANGGAL</th>
                <th rowspan="2">PERGI TANGGAL</th>
                <th rowspan="2">KET</th>
            </tr>
            <tr>
                <th>L</th>
                <th>P</th>
                <th>KEBANGSAAN</th>
                <th>KETURUNAN</th>
            </tr>
        </table>

        <!-- TANDA TANGAN -->
        <div style="margin-top: 28px;">
            <table class="sig">
                <tr>
                    <td class="wide">Mengetahui,</td>
                    <td class="spacer"></td>
                    <td class="wide"><?= $user->desaname ?>, <?= tgl_indo(getCurrentDate('Y-m-d')) ?></td>
                </tr>
                <tr>
                    <td class="wide">Kepala Desa</td>
                    <td class="spacer"></td>
                    <td class="wide">Sekretaris Desa</td>
                </tr>
            </table>

            <div style="height: 40mm;"></div>

            <table class="sig">
                <tr>
                    <td class="wide">(_______________)</td>
                    <td class="spacer"></td>
                    <td class="wide">(_______________)</td>
                </tr>
            </table>
        </div>

    </div>
</body>

</html>