<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga</title>

    <style>
        .table {
            border-collapse: collapse;
            width: 100%;
        }

        .table th,
        .table td {
            border: 1px solid #000;
            padding: 2px;
            text-align: left;
            font-size: 5px;
            vertical-align: top;
        }

        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }

        .center {
            text-align: center;
            width: 100%;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }

        .summary-table {
            margin-bottom: 20px;
            border-collapse: collapse;
        }

        .summary-table th,
        .summary-table td {
            border: 1px solid #000;
            padding: 8px;
            font-size: 10px;
        }

        .summary-table th {
            background-color: #e6e6e6;
            text-align: center;
        }

        .text-right {
            text-align: right;
        }
    </style>
</head>

<body>

    <table class="center" style="text-align:center; margin-bottom : 30px;">
        <tr>
            <th rowspan="4"><img src="<?= base_url('/assets/img/logo-bpd.png') ?>" width="100px;" height="100px;" style="margin-right:50px;" alt=""></th>
            <th><b>PEMERINTAH DESA</b></th>
        </tr>
        <tr>
            <th><b>DESA <?= $user->desaname ?> KECAMATAN <?= $user->kecamatanname ?></b></th>
        </tr>
        <tr>
            <th><b>KABUPATEN <?= $user->kabkotaname ?></b></th>
        </tr>
        <tr>
            <th><b>PROVINSI</b></th>
        </tr>
    </table>

    <b>
        <hr>
    </b>
    <div style="text-align: center; margin-bottom: 30px;">
        <p>
            BUKU KARTU TANDA PENDUDUK DAN BUKU KARTU KELUARGA
            <br>
            PEMERINTAH DESA <?= $user->desaname ?>
        </p>
    </div>

    <!-- Summary Statistics -->
    <div style="margin-bottom: 30px;">
        <h4>RINGKASAN DATA KEPENDUDUKAN</h4>
        <table class="summary-table" style="width: 70%;">
            <tr>
                <th>Total Penduduk</th>
                <td class="text-right"><?= number_format($statistics['totals']->total_ktpkk ?? 0) ?> orang</td>
            </tr>
            <tr>
                <th>Total Kartu Keluarga</th>
                <td class="text-right"><?= number_format($statistics['total_kk']->total_kk ?? 0) ?> KK</td>
            </tr>
        </table>
    </div>

    <!-- By Gender Summary -->
    <?php if (!empty($statistics['by_gender'])): ?>
        <div style="margin-bottom: 20px;">
            <h4>RINGKASAN PER JENIS KELAMIN</h4>
            <table class="summary-table" style="width: 50%;">
                <tr>
                    <th>Jenis Kelamin</th>
                    <th>Jumlah</th>
                </tr>
                <?php foreach ($statistics['by_gender'] as $gender): ?>
                    <tr>
                        <td><?= $gender->jenis_kelamin ?: 'Tidak Diketahui' ?></td>
                        <td class="text-right"><?= number_format($gender->jumlah) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>

    <!-- By Agama Summary -->
    <?php if (!empty($statistics['by_agama'])): ?>
        <div style="margin-bottom: 20px;">
            <h4>RINGKASAN PER AGAMA</h4>
            <table class="summary-table" style="width: 60%;">
                <tr>
                    <th>Agama</th>
                    <th>Jumlah</th>
                </tr>
                <?php foreach ($statistics['by_agama'] as $agama): ?>
                    <tr>
                        <td><?= $agama->agama ?: 'Tidak Diketahui' ?></td>
                        <td class="text-right"><?= number_format($agama->jumlah) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>

    <!-- By Status Perkawinan Summary -->
    <?php if (!empty($statistics['by_status_perkawinan'])): ?>
        <div style="margin-bottom: 20px;">
            <h4>RINGKASAN PER STATUS PERKAWINAN</h4>
            <table class="summary-table" style="width: 60%;">
                <tr>
                    <th>Status Perkawinan</th>
                    <th>Jumlah</th>
                </tr>
                <?php foreach ($statistics['by_status_perkawinan'] as $status): ?>
                    <tr>
                        <td><?= $status->status_perkawinan ?: 'Tidak Diketahui' ?></td>
                        <td class="text-right"><?= number_format($status->jumlah) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>

    <!-- Main KTP & KK Table -->
    <h4>DAFTAR KARTU TANDA PENDUDUK DAN KARTU KELUARGA</h4>
    <table class="table">
        <tr>
            <th width="10">No</th>
            <th width="25">No. KK</th>
            <th width="60">Nama Lengkap</th>
            <th width="25">NIK</th>
            <th width="15">L/P</th>
            <th width="30">Tempat/Tgl Lahir</th>
            <th width="15">Gol. Darah</th>
            <th width="20">Agama</th>
            <th width="30">Pendidikan</th>
            <th width="30">Pekerjaan</th>
            <th width="50">Alamat</th>
            <th width="25">Status Kawin</th>
            <th width="30">Tempat/Tgl Dikeluarkan</th>
            <th width="30">Status Keluarga</th>
            <th width="15">WNI/WNA</th>
            <th width="30">Nama Ayah</th>
            <th width="30">Nama Ibu</th>
            <th width="20">Tgl Masuk Desa</th>
            <th width="30">Keterangan</th>
        </tr>

        <?php foreach ($ktpkk as $key => $value) : ?>
            <tr>
                <td width="10"><?= $value->nomor_urut ?? ($key + 1) ?></td>
                <td width="25"><?= wordwrap($value->no_kk ?? '-', 8, '<br>', true) ?></td>
                <td width="60"><?= wordwrap($value->nama_lengkap ?? '-', 15, '<br>', true) ?></td>
                <td width="25"><?= wordwrap($value->nik ?? '-', 8, '<br>', true) ?></td>
                <td width="15"><?= $value->jenis_kelamin == 'Laki-laki' ? 'L' : ($value->jenis_kelamin == 'Perempuan' ? 'P' : '-') ?></td>
                <td width="30"><?= wordwrap(($value->tempat_lahir ?? '') . ($value->tanggal_lahir ? ', ' . date('d/m/Y', strtotime($value->tanggal_lahir)) : ''), 10, '<br>', true) ?></td>
                <td width="15"><?= $value->gol_darah ?? '-' ?></td>
                <td width="20"><?= wordwrap($value->agama ?? '-', 8, '<br>', true) ?></td>
                <td width="30"><?= wordwrap($value->pendidikan ?? '-', 10, '<br>', true) ?></td>
                <td width="30"><?= wordwrap($value->pekerjaan ?? '-', 10, '<br>', true) ?></td>
                <td width="50"><?= wordwrap($value->alamat ?? '-', 15, '<br>', true) ?></td>
                <td width="25"><?= wordwrap($value->status_perkawinan ?? '-', 8, '<br>', true) ?></td>
                <td width="30"><?= wordwrap(($value->tempat_dikeluarkan ?? '') . ($value->tanggal_dikeluarkan ? ', ' . date('d/m/Y', strtotime($value->tanggal_dikeluarkan)) : ''), 10, '<br>', true) ?></td>
                <td width="30"><?= wordwrap($value->status_hubungan_keluarga ?? '-', 10, '<br>', true) ?></td>
                <td width="15"><?= $value->kewarganegaraan ?? 'WNI' ?></td>
                <td width="30"><?= wordwrap($value->nama_ayah ?? '-', 10, '<br>', true) ?></td>
                <td width="30"><?= wordwrap($value->nama_ibu ?? '-', 10, '<br>', true) ?></td>
                <td width="20"><?= $value->tanggal_masuk_desa ? date('d/m/Y', strtotime($value->tanggal_masuk_desa)) : '-' ?></td>
                <td width="30"><?= wordwrap($value->keterangan ?? '-', 10, '<br>', true) ?></td>
            </tr>
        <?php endforeach; ?>
    </table>

    <!-- Status Hubungan Keluarga Summary -->
    <?php if (!empty($statistics['by_status_hubungan'])): ?>
        <div style="margin-top: 30px;">
            <h4>RINGKASAN PER STATUS HUBUNGAN KELUARGA</h4>
            <table class="summary-table" style="width: 70%;">
                <tr>
                    <th>Status Hubungan Keluarga</th>
                    <th>Jumlah</th>
                </tr>
                <?php foreach ($statistics['by_status_hubungan'] as $status): ?>
                    <tr>
                        <td><?= $status->status_hubungan_keluarga ?: 'Tidak Diketahui' ?></td>
                        <td class="text-right"><?= number_format($status->jumlah) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>

    <!-- Age Group Summary -->
    <?php if (!empty($statistics['by_age'])): ?>
        <div style="margin-top: 30px;">
            <h4>RINGKASAN PER KELOMPOK UMUR</h4>
            <table class="summary-table" style="width: 60%;">
                <tr>
                    <th>Kelompok Umur</th>
                    <th>Jumlah</th>
                </tr>
                <?php foreach ($statistics['by_age'] as $age): ?>
                    <tr>
                        <td><?= $age->kelompok_umur ?></td>
                        <td class="text-right"><?= number_format($age->jumlah) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>

    <!-- Monthly Registration -->
    <?php if (!empty($statistics['by_month'])): ?>
        <div style="margin-top: 30px;">
            <h4>PENERBITAN KTP/KK PER BULAN TAHUN <?= date('Y') ?></h4>
            <table class="summary-table" style="width: 60%;">
                <tr>
                    <th>Bulan</th>
                    <th>Jumlah Penerbitan</th>
                </tr>
                <?php
                $months = [
                    1 => 'Januari',
                    2 => 'Februari',
                    3 => 'Maret',
                    4 => 'April',
                    5 => 'Mei',
                    6 => 'Juni',
                    7 => 'Juli',
                    8 => 'Agustus',
                    9 => 'September',
                    10 => 'Oktober',
                    11 => 'November',
                    12 => 'Desember'
                ];
                foreach ($statistics['by_month'] as $month):
                ?>
                    <tr>
                        <td><?= $months[$month->bulan] ?></td>
                        <td class="text-right"><?= number_format($month->jumlah) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>

    <!-- Keterangan -->
    <div style="margin-top: 30px;">
        <h4>KETERANGAN</h4>
        <table class="summary-table" style="width: 80%;">
            <tr>
                <td><b>KTP</b></td>
                <td>Kartu Tanda Penduduk</td>
                <td><b>KK</b></td>
                <td>Kartu Keluarga</td>
            </tr>
            <tr>
                <td><b>WNI</b></td>
                <td>Warga Negara Indonesia</td>
                <td><b>WNA</b></td>
                <td>Warga Negara Asing</td>
            </tr>
            <tr>
                <td><b>L</b></td>
                <td>Laki-laki</td>
                <td><b>P</b></td>
                <td>Perempuan</td>
            </tr>
        </table>
    </div>

    <div style="margin-top: 50px;">
        <table>
            <tr>
                <td width="400" style="text-align: center;">Mengetahui,</td>
                <td width="175"></td>
                <td width="400" style="text-align: center;"><?= $user->desaname ?>, <?= tgl_indo(getCurrentDate('Y-m-d')) ?>,</td>
            </tr>

            <tr>
                <td width="400" style="text-align: center;">Kepala Desa</td>
                <td width="175"></td>
                <td width="400" style="text-align: center;">Sekretaris Desa</td>
            </tr>
        </table>

        <div style="margin-top: 65px;">
            <table>
                <tr>
                    <td width="400" style="text-align: center;">(_______________)</td>
                    <td width="175"></td>
                    <td width="400" style="text-align: center;">(_______________)</td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html>