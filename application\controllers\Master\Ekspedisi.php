<?php

use Dompdf\Dompdf;
use Dompdf\Options;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Ekspedisis $ekspedisis
 * @property Superadmins $admins
 */
class Ekspedisi extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Ekspedisis', 'ekspedisis');
        $this->load->model('Superadmins', 'admins');
    }

    public function index()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($startdate != null) {
            $where['DATE(a.tanggal_pengiriman) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_pengiriman) <='] = $enddate;
        }

        $data = array();
        $data['title'] = 'Buku Ekspedisi';
        $data['content'] = 'master/ekspedisi/index';

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();

            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }

            $this->ekspedisis->where_in('userid', $bpdid);
        }

        $data['ekspedisi'] = $this->ekspedisis->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);
        $data['startdate'] = $startdate;
        $data['enddate'] = $enddate;

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Tambah Ekspedisi';
        $data['content'] = 'master/ekspedisi/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        }

        $tanggal_pengiriman = getPost('tanggal_pengiriman');
        $tanggal_surat = getPost('tanggal_surat');
        $nomor_surat = getPost('nomor_surat');
        $isi_singkat_surat = getPost('isi_singkat_surat');
        $ditujukan_kepada = getPost('ditujukan_kepada');
        $keterangan = getPost('keterangan');

        if (empty($tanggal_pengiriman)) {
            return JSONResponseDefault('FAILED', 'Tanggal pengiriman harus diisi');
        }

        if (empty($nomor_surat)) {
            return JSONResponseDefault('FAILED', 'Nomor surat harus diisi');
        }

        if (empty($isi_singkat_surat)) {
            return JSONResponseDefault('FAILED', 'Isi singkat surat harus diisi');
        }

        if (empty($ditujukan_kepada)) {
            return JSONResponseDefault('FAILED', 'Ditujukan kepada harus diisi');
        }

        $insert = array();
        $insert['tanggal_pengiriman'] = $tanggal_pengiriman;
        $insert['tanggal_surat'] = $tanggal_surat;
        $insert['nomor_surat'] = $nomor_surat;
        $insert['isi_singkat_surat'] = $isi_singkat_surat;
        $insert['ditujukan_kepada'] = $ditujukan_kepada;
        $insert['keterangan'] = $keterangan;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();
        $insert['userid'] = getCurrentIdUser();

        $this->ekspedisis->insert($insert);

        return JSONResponseDefault('OK', 'Data ekspedisi berhasil disimpan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $get = $this->ekspedisis->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/ekspedisi'));
        }

        $data = array();
        $data['title'] = 'Edit Ekspedisi';
        $data['content'] = 'master/ekspedisi/edit';
        $data['row'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->ekspedisis->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Ekspedisi tidak ditemukan');
        }

        $tanggal_pengiriman = getPost('tanggal_pengiriman');
        $tanggal_surat = getPost('tanggal_surat');
        $nomor_surat = getPost('nomor_surat');
        $isi_singkat_surat = getPost('isi_singkat_surat');
        $ditujukan_kepada = getPost('ditujukan_kepada');
        $keterangan = getPost('keterangan');

        if (empty($tanggal_pengiriman)) {
            return JSONResponseDefault('FAILED', 'Tanggal pengiriman harus diisi');
        }

        if (empty($nomor_surat)) {
            return JSONResponseDefault('FAILED', 'Nomor surat harus diisi');
        }

        if (empty($isi_singkat_surat)) {
            return JSONResponseDefault('FAILED', 'Isi singkat surat harus diisi');
        }

        if (empty($ditujukan_kepada)) {
            return JSONResponseDefault('FAILED', 'Ditujukan kepada harus diisi');
        }

        $update = array();
        $update['tanggal_pengiriman'] = $tanggal_pengiriman;
        $update['tanggal_surat'] = $tanggal_surat;
        $update['nomor_surat'] = $nomor_surat;
        $update['isi_singkat_surat'] = $isi_singkat_surat;
        $update['ditujukan_kepada'] = $ditujukan_kepada;
        $update['keterangan'] = $keterangan;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->ekspedisis->update($update, array('id' => $id));

        return JSONResponseDefault('OK', 'Data ekspedisi berhasil diupdate');
    }

    public function delete($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->ekspedisis->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Ekspedisi tidak ditemukan');
        }

        $this->ekspedisis->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Data ekspedisi berhasil dihapus');
    }

    public function export()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($startdate != null) {
            $where['DATE(a.tanggal_pengiriman) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_pengiriman) <='] = $enddate;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();

            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }

            $this->ekspedisis->where_in('userid', $bpdid);
        }

        $ekspedisi = $this->ekspedisis->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/ekspedisi/export', array(
            'ekspedisi' => $ekspedisi,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $this->ekspedisis->getStatistics(getCurrentUser()->id ?? null)
        ), true));
        $dompdf->render();
        $dompdf->stream('Buku Ekspedisi.pdf', array("Attachment" => false));
    }
}
