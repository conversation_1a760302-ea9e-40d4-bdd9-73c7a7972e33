<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'Dashboard/default';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

$route['guest/listofattendance'] = 'Guest/ListOfAttendance';
$route['guest/listofattendance/scan'] = 'Guest/ListOfAttendance/scan';
$route['guest/listofattendance/scan/process'] = 'Guest/ListOfAttendance/process';

$route['sitemap'] = 'Sitemap';

$route['auth/login'] = 'Auth/login';
$route['auth/login/process'] = 'Auth/process_login';
$route['auth/logout'] = 'Auth/logout';
$route['auth/forgot'] = 'Auth/forgot';
$route['auth/forgot/process'] = 'Auth/process_forgot';
$route['auth/reset/(:any)'] = 'Auth/reset/$1';
$route['auth/reset/(:any)/process'] = 'Auth/process_reset/$1';

$route['account/profile'] = 'Account/profile';
$route['account/profile/process'] = 'Account/process_change_profile';

$route['account/password'] = 'Account/password';
$route['account/password/process'] = 'Account/process_change_password';

$route['dashboard/report'] = 'Dashboard/report';
$route['dashboard/userid'] = 'Dashboard/userid';
$route['dashboard/attendance'] = 'Dashboard/attendance';
$route['dashboard/export'] = 'Dashboard/export';

$route['master/select/kecamatan'] = 'Master/Select/kecamatan';
$route['master/select/desa'] = 'Master/Select/desa';

$route['master/superadmin'] = 'Master/SuperAdmin';
$route['master/superadmin/add'] = 'Master/SuperAdmin/add';
$route['master/superadmin/add/process'] = 'Master/SuperAdmin/process_add';
$route['master/superadmin/edit/(:num)'] = 'Master/SuperAdmin/edit/$1';
$route['master/superadmin/edit/(:num)/process'] = 'Master/SuperAdmin/process_edit/$1';

$route['master/pmd'] = 'Master/PMD';
$route['master/pmd/add'] = 'Master/PMD/add';
$route['master/pmd/add/process'] = 'Master/PMD/process_add';
$route['master/pmd/edit/(:num)'] = 'Master/PMD/edit/$1';
$route['master/pmd/edit/(:num)/process'] = 'Master/PMD/process_edit/$1';

$route['master/village'] = 'Master/Village';
$route['master/village/add'] = 'Master/Village/add';
$route['master/village/add/process'] = 'Master/Village/process_add';
$route['master/village/edit/(:num)'] = 'Master/Village/edit/$1';
$route['master/village/edit/(:num)/process'] = 'Master/Village/process_edit/$1';
$route['master/village/operator/(:num)'] = 'Master/Village/operator/$1';

$route['master/bpd'] = 'Master/BPD';
$route['master/bpd/add'] = 'Master/BPD/add';
$route['master/bpd/add/process'] = 'Master/BPD/process_add';
$route['master/bpd/edit/(:num)'] = 'Master/BPD/edit/$1';
$route['master/bpd/edit/(:num)/process'] = 'Master/BPD/process_edit/$1';
$route['master/bpd/monitoring'] = 'Master/BPD/monitoring';

$route['master/notulenrapat'] = 'Master/NotulenRapat';
$route['master/notulenrapat/add'] = 'Master/NotulenRapat/add';
$route['master/notulenrapat/add/process'] = 'Master/NotulenRapat/process_add';
$route['master/notulenrapat/edit/(:num)'] = 'Master/NotulenRapat/edit/$1';
$route['master/notulenrapat/edit/(:num)/process'] = 'Master/NotulenRapat/process_edit/$1';
$route['master/notulenrapat/export'] = 'Master/NotulenRapat/export';

$route['master/kegiatanbpd'] = 'Master/KegiatanBPD';
$route['master/kegiatanbpd/add'] = 'Master/KegiatanBPD/add';
$route['master/kegiatanbpd/add/process'] = 'Master/KegiatanBPD/process_add';
$route['master/kegiatanbpd/edit/(:num)'] = 'Master/KegiatanBPD/edit/$1';
$route['master/kegiatanbpd/edit/(:num)/process'] = 'Master/KegiatanBPD/process_edit/$1';
$route['master/kegiatanbpd/export'] = 'Master/KegiatanBPD/export';

$route['master/suratkeluar'] = 'Master/SuratKeluar';
$route['master/suratkeluar/export'] = 'Master/SuratKeluar/export';
$route['master/suratkeluar/add'] = 'Master/SuratKeluar/add';
$route['master/suratkeluar/add/process'] = 'Master/SuratKeluar/process_add';
$route['master/suratkeluar/edit/(:num)'] = 'Master/SuratKeluar/edit/$1';
$route['master/suratkeluar/edit/(:num)/process'] = 'Master/SuratKeluar/process_edit/$1';

$route['master/suratmasuk'] = 'Master/SuratMasuk';
$route['master/suratmasuk/export'] = 'Master/SuratMasuk/export';
$route['master/suratmasuk/add'] = 'Master/SuratMasuk/add';
$route['master/suratmasuk/add/process'] = 'Master/SuratMasuk/process_add';
$route['master/suratmasuk/edit/(:num)'] = 'Master/SuratMasuk/edit/$1';
$route['master/suratmasuk/edit/(:num)/process'] = 'Master/SuratMasuk/process_edit/$1';

// Buku Agenda (Unified Surat Masuk & Surat Keluar)
$route['master/bukuagenda'] = 'Master/BukuAgenda';
$route['master/bukuagenda/export'] = 'Master/BukuAgenda/export';
$route['master/bukuagenda/add'] = 'Master/BukuAgenda/add';
$route['master/bukuagenda/add/process'] = 'Master/BukuAgenda/process_add';
$route['master/bukuagenda/edit/(:num)'] = 'Master/BukuAgenda/edit/$1';
$route['master/bukuagenda/edit/(:num)/process'] = 'Master/BukuAgenda/process_edit/$1';
$route['master/bukuagenda/delete/(:num)'] = 'Master/BukuAgenda/delete/$1';

$route['master/guestbook'] = 'Master/GuestBook';
$route['master/guestbook/export'] = 'Master/GuestBook/export';

$route['master/anggotabpd'] = 'Master/AnggotaBPD';
$route['master/anggotabpd/export'] = 'Master/AnggotaBPD/export';
$route['master/anggotabpd/sync'] = 'Master/AnggotaBPD/sync';

$route['master/aspirasimasyarakat'] = 'Master/AspirasiMasyarakat';
$route['master/aspirasimasyarakat/export'] = 'Master/AspirasiMasyarakat/export';

$route['master/daftarhadir'] = 'Master/DaftarHadir';
$route['master/daftarhadir/export'] = 'Master/DaftarHadir/export';
$route['master/daftarhadir/history'] = 'Master/DaftarHadir/history';

$route['master/keputusanbpd'] = 'Master/KeputusanBPD';
$route['master/keputusanbpd/export'] = 'Master/KeputusanBPD/export';

$route['master/keputusankepaladesa'] = 'Master/KeputusanKepalaDesa';
$route['master/keputusankepaladesa/add'] = 'Master/KeputusanKepalaDesa/add';
$route['master/keputusankepaladesa/add/process'] = 'Master/KeputusanKepalaDesa/process_add';
$route['master/keputusankepaladesa/edit/(:num)'] = 'Master/KeputusanKepalaDesa/edit/$1';
$route['master/keputusankepaladesa/process_edit'] = 'Master/KeputusanKepalaDesa/process_edit';
$route['master/keputusankepaladesa/delete/(:num)'] = 'Master/KeputusanKepalaDesa/delete/$1';
$route['master/keputusankepaladesa/export'] = 'Master/KeputusanKepalaDesa/export';

$route['master/bukuinventarisdesa'] = 'Master/BukuInventarisDesa/index';
$route['master/bukuinventarisdesa/export'] = 'Master/BukuInventarisDesa/export';

$route['master/aparatdesa'] = 'Master/AparatDesa';
$route['master/aparatdesa/export'] = 'Master/AparatDesa/export';

$route['master/tanahkasdesa'] = 'Master/TanahKasDesa/index';
$route['master/tanahkasdesa/export'] = 'Master/TanahKasDesa/export';

$route['master/tanahdidesa'] = 'Master/TanahDiDesa/index';
$route['master/tanahdidesa/export'] = 'Master/TanahDiDesa/export';

$route['master/ekspedisi'] = 'Master/Ekspedisi/index';
$route['master/ekspedisi/add'] = 'Master/Ekspedisi/add';
$route['master/ekspedisi/add/process'] = 'Master/Ekspedisi/process_add';
$route['master/ekspedisi/edit/(:num)'] = 'Master/Ekspedisi/edit/$1';
$route['master/ekspedisi/edit/(:num)/process'] = 'Master/Ekspedisi/process_edit/$1';
$route['master/ekspedisi/delete/(:num)'] = 'Master/Ekspedisi/delete/$1';
$route['master/ekspedisi/export'] = 'Master/Ekspedisi/export';

$route['master/indukpenduduk'] = 'Master/IndukPenduduk/index';
$route['master/indukpenduduk/export'] = 'Master/IndukPenduduk/export';

$route['master/mutasipenduduk'] = 'Master/MutasiPenduduk/index';
$route['master/mutasipenduduk/export'] = 'Master/MutasiPenduduk/export';

$route['master/rekappenduduk'] = 'Master/RekapPenduduk/index';
$route['master/rekappenduduk/export'] = 'Master/RekapPenduduk/export';

$route['master/penduduksementara'] = 'Master/PendudukSementara/index';
$route['master/penduduksementara/export'] = 'Master/PendudukSementara/export';

$route['master/ktpkk/export'] = 'Master/KtpKk/export';
$route['master/ktpkk/statistics'] = 'Master/KtpKk/statistics';
$route['master/ktpkk/search'] = 'Master/KtpKk/search';
$route['master/ktpkk/dashboard'] = 'Master/KtpKk/dashboard';
$route['master/ktpkk/chart_data'] = 'Master/KtpKk/chart_data';
$route['master/ktpkk/import'] = 'Master/KtpKk/import';
$route['master/ktpkk/process_import'] = 'Master/KtpKk/process_import';
$route['master/ktpkk/export_excel'] = 'Master/KtpKk/export_excel';
$route['master/ktpkk/family_members'] = 'Master/KtpKk/family_members';
$route['master/ktpkk/unique_nokk'] = 'Master/KtpKk/unique_nokk';

$route['master/pendapatanbelanjapembiayaan/export'] = 'Master/PendapatanBelanjaPembiayaan/export';
$route['master/pendapatanbelanjapembiayaan/statistics'] = 'Master/PendapatanBelanjaPembiayaan/statistics';
$route['master/pendapatanbelanjapembiayaan/search'] = 'Master/PendapatanBelanjaPembiayaan/search';
$route['master/pendapatanbelanjapembiayaan/dashboard'] = 'Master/PendapatanBelanjaPembiayaan/dashboard';
$route['master/pendapatanbelanjapembiayaan/controlling'] = 'Master/PendapatanBelanjaPembiayaan/controlling';
$route['master/pendapatanbelanjapembiayaan/chart_data'] = 'Master/PendapatanBelanjaPembiayaan/chart_data';
$route['master/pendapatanbelanjapembiayaan/import'] = 'Master/PendapatanBelanjaPembiayaan/import';
$route['master/pendapatanbelanjapembiayaan/process_import'] = 'Master/PendapatanBelanjaPembiayaan/process_import';
$route['master/pendapatanbelanjapembiayaan/export_excel'] = 'Master/PendapatanBelanjaPembiayaan/export_excel';
$route['master/pendapatanbelanjapembiayaan/calculate_controlling'] = 'Master/PendapatanBelanjaPembiayaan/calculate_controlling';

$route['master/rkpdes/export'] = 'Master/Rkpdes/export';
$route['master/rkpdes/statistics'] = 'Master/Rkpdes/statistics';
$route['master/rkpdes/search'] = 'Master/Rkpdes/search';
$route['master/rkpdes/dashboard'] = 'Master/Rkpdes/dashboard';
$route['master/rkpdes/chart_data'] = 'Master/Rkpdes/chart_data';
$route['master/rkpdes/import'] = 'Master/Rkpdes/import';
$route['master/rkpdes/process_import'] = 'Master/Rkpdes/process_import';
$route['master/rkpdes/export_excel'] = 'Master/Rkpdes/export_excel';

$route['master/kegiatanpembangunan/export'] = 'Master/KegiatanPembangunan/export';
$route['master/kegiatanpembangunan/cetak_permendagri'] = 'Master/KegiatanPembangunan/cetak_permendagri';
$route['master/kegiatanpembangunan/statistics'] = 'Master/KegiatanPembangunan/statistics';
$route['master/kegiatanpembangunan/search'] = 'Master/KegiatanPembangunan/search';
$route['master/kegiatanpembangunan/dashboard'] = 'Master/KegiatanPembangunan/dashboard';
$route['master/kegiatanpembangunan/chart_data'] = 'Master/KegiatanPembangunan/chart_data';
$route['master/kegiatanpembangunan/import'] = 'Master/KegiatanPembangunan/import';
$route['master/kegiatanpembangunan/process_import'] = 'Master/KegiatanPembangunan/process_import';
$route['master/kegiatanpembangunan/export_excel'] = 'Master/KegiatanPembangunan/export_excel';

$route['master/inventarisasihasilpembangunan/export'] = 'Master/InventarisasiHasilPembangunan/export';
$route['master/inventarisasihasilpembangunan/statistics'] = 'Master/InventarisasiHasilPembangunan/statistics';
$route['master/inventarisasihasilpembangunan/search'] = 'Master/InventarisasiHasilPembangunan/search';
$route['master/inventarisasihasilpembangunan/dashboard'] = 'Master/InventarisasiHasilPembangunan/dashboard';
$route['master/inventarisasihasilpembangunan/chart_data'] = 'Master/InventarisasiHasilPembangunan/chart_data';
$route['master/inventarisasihasilpembangunan/import'] = 'Master/InventarisasiHasilPembangunan/import';
$route['master/inventarisasihasilpembangunan/process_import'] = 'Master/InventarisasiHasilPembangunan/process_import';
$route['master/inventarisasihasilpembangunan/export_excel'] = 'Master/InventarisasiHasilPembangunan/export_excel';

$route['master/kaderpemberdayaan/export'] = 'Master/KaderPemberdayaan/export';
$route['master/kaderpemberdayaan/statistics'] = 'Master/KaderPemberdayaan/statistics';
$route['master/kaderpemberdayaan/search'] = 'Master/KaderPemberdayaan/search';
$route['master/kaderpemberdayaan/dashboard'] = 'Master/KaderPemberdayaan/dashboard';
$route['master/kaderpemberdayaan/chart_data'] = 'Master/KaderPemberdayaan/chart_data';
$route['master/kaderpemberdayaan/import'] = 'Master/KaderPemberdayaan/import';
$route['master/kaderpemberdayaan/process_import'] = 'Master/KaderPemberdayaan/process_import';
$route['master/kaderpemberdayaan/export_excel'] = 'Master/KaderPemberdayaan/export_excel';

$route['master/musyawarahdesa/export'] = 'Master/MusyawarahDesa/export';
$route['master/musyawarahdesa/statistics'] = 'Master/MusyawarahDesa/statistics';
$route['master/musyawarahdesa/search'] = 'Master/MusyawarahDesa/search';
$route['master/musyawarahdesa/dashboard'] = 'Master/MusyawarahDesa/dashboard';
$route['master/musyawarahdesa/chart_data'] = 'Master/MusyawarahDesa/chart_data';
$route['master/musyawarahdesa/download_file/(:num)/(:any)'] = 'Master/MusyawarahDesa/download_file/$1/$2';
$route['master/musyawarahdesa/import'] = 'Master/MusyawarahDesa/import';
$route['master/musyawarahdesa/process_import'] = 'Master/MusyawarahDesa/process_import';
$route['master/musyawarahdesa/export_excel'] = 'Master/MusyawarahDesa/export_excel';

$route['master/lembagakemasyarakatan/export'] = 'Master/LembagaKemasyarakatan/export';
$route['master/lembagakemasyarakatan/statistics'] = 'Master/LembagaKemasyarakatan/statistics';
$route['master/lembagakemasyarakatan/search'] = 'Master/LembagaKemasyarakatan/search';
$route['master/lembagakemasyarakatan/dashboard'] = 'Master/LembagaKemasyarakatan/dashboard';
$route['master/lembagakemasyarakatan/chart_data'] = 'Master/LembagaKemasyarakatan/chart_data';
$route['master/lembagakemasyarakatan/import'] = 'Master/LembagaKemasyarakatan/import';
$route['master/lembagakemasyarakatan/process_import'] = 'Master/LembagaKemasyarakatan/process_import';
$route['master/lembagakemasyarakatan/export_excel'] = 'Master/LembagaKemasyarakatan/export_excel';

// Routing untuk Cetak Administrasi berdasarkan Permendagri 46
$route['cetak/buku_penduduk/(:num)'] = 'Master/CetakAdministrasi/buku_penduduk/$1';
$route['cetak/buku_penduduk'] = 'Master/CetakAdministrasi/buku_penduduk';
$route['cetak/buku_mutasi_penduduk/(:num)'] = 'Master/CetakAdministrasi/buku_mutasi_penduduk/$1';
$route['cetak/buku_mutasi_penduduk'] = 'Master/CetakAdministrasi/buku_mutasi_penduduk';
$route['cetak/buku_rekapitulasi_penduduk/(:num)'] = 'Master/CetakAdministrasi/buku_rekapitulasi_penduduk/$1';
$route['cetak/buku_rekapitulasi_penduduk'] = 'Master/CetakAdministrasi/buku_rekapitulasi_penduduk';
$route['cetak/buku_data_agregat_penduduk/(:num)'] = 'Master/CetakAdministrasi/buku_data_agregat_penduduk/$1';
$route['cetak/buku_data_agregat_penduduk'] = 'Master/CetakAdministrasi/buku_data_agregat_penduduk';
$route['cetak/buku_data_tanah_desa/(:num)'] = 'Master/CetakAdministrasi/buku_data_tanah_desa/$1';
$route['cetak/buku_data_tanah_desa'] = 'Master/CetakAdministrasi/buku_data_tanah_desa';
$route['cetak/buku_kegiatan_pembangunan/(:num)'] = 'Master/CetakAdministrasi/buku_kegiatan_pembangunan/$1';
$route['cetak/buku_inventarisasi_hasil_pembangunan/(:num)'] = 'Master/CetakAdministrasi/buku_inventarisasi_hasil_pembangunan/$1';
$route['cetak/buku_kader_pemberdayaan/(:num)'] = 'Master/CetakAdministrasi/buku_kader_pemberdayaan/$1';
$route['cetak/kegiatan_musyawarah_desa/(:num)'] = 'Master/CetakAdministrasi/kegiatan_musyawarah_desa/$1';
$route['cetak/kegiatan_lembaga_kemasyarakatan/(:num)'] = 'Master/CetakAdministrasi/kegiatan_lembaga_kemasyarakatan/$1';

$route['master/peraturandesa'] = 'Master/PeraturanDesa';
$route['master/peraturandesa/add'] = 'Master/PeraturanDesa/add';
$route['master/peraturandesa/add/process'] = 'Master/PeraturanDesa/process_add';
$route['master/peraturandesa/edit/(:num)'] = 'Master/PeraturanDesa/edit/$1';
$route['master/peraturandesa/process_edit'] = 'Master/PeraturanDesa/process_edit';
$route['master/peraturandesa/delete/(:num)'] = 'Master/PeraturanDesa/delete/$1';
$route['master/peraturandesa/export'] = 'Master/PeraturanDesa/export';

$route['master/operator'] = 'Master/Operator';
$route['master/operator/add'] = 'Master/Operator/add';
$route['master/operator/add/process'] = 'Master/Operator/process_add';
$route['master/operator/edit/(:num)'] = 'Master/Operator/edit/$1';
$route['master/operator/edit/(:num)/process'] = 'Master/Operator/process_edit/$1';
$route['master/operator/monitoring'] = 'Master/Operator/monitoring';

$route['master/kecamatan'] = 'Master/Kecamatan';
$route['master/kecamatan/add'] = 'Master/Kecamatan/add';
$route['master/kecamatan/add/process'] = 'Master/Kecamatan/process_add';
$route['master/kecamatan/edit/(:num)'] = 'Master/Kecamatan/edit/$1';
$route['master/kecamatan/edit/(:num)/process'] = 'Master/Kecamatan/process_edit/$1';

$route['master/kepdes'] = 'Master/KepDes';
$route['master/kepdes/process'] = 'Master/KepDes/process_kepdes';

$route['master/user_guide'] = 'Master/UserGuide';
$route['master/user_guide/add'] = 'Master/UserGuide/add';
$route['master/user_guide/add/process'] = 'Master/UserGuide/process_add';
$route['master/user_guide/edit/(:num)'] = 'Master/UserGuide/edit/$1';
$route['master/user_guide/edit/(:num)/process'] = 'Master/UserGuide/process_edit/$1';
$route['master/user_guide/delete'] = 'Master/UserGuide/process_delete';

// Block access to old inventarisdesa system
$route['master/inventarisdesa'] = 'Dashboard/default';
$route['master/inventarisdesa/(.*)'] = 'Dashboard/default';


// Lembaran & Berita Desa routes (satu fitur)
$route['master/lembarandesa'] = 'Master/LembaranDesa/index';
$route['master/lembarandesa/export'] = 'Master/LembaranDesa/export';
$route['master/lembarandesa/add'] = 'Master/LembaranDesa/add';
$route['master/lembarandesa/edit/(:num)'] = 'Master/LembaranDesa/edit/$1';
$route['master/lembarandesa/process_add'] = 'Master/LembaranDesa/process_add';
$route['master/lembarandesa/process_edit'] = 'Master/LembaranDesa/process_edit';
$route['master/lembarandesa/delete/(:num)'] = 'Master/LembaranDesa/delete/$1';

$route['master/(:any)'] = 'CRUDCore/index/$1';
$route['master/(:any)/add'] = 'CRUDCore/add/$1';
$route['master/(:any)/add/process'] = 'CRUDCore/process_add/$1';
$route['master/(:any)/edit/(:num)'] = 'CRUDCore/edit/$1/$2';
$route['master/(:any)/edit/(:num)/process'] = 'CRUDCore/process_edit/$1/$2';
$route['master/(:any)/detail/(:num)'] = 'CRUDCore/detail/$1/$2';
$route['master/(:any)/delete'] = 'CRUDCore/process_delete/$1';

$route['sppd'] = 'SPPD';
$route['sppd/add'] = 'SPPD/add';
$route['sppd/add/process'] = 'SPPD/process_add';
$route['sppd/edit/(:num)'] = 'SPPD/edit/$1';
$route['sppd/edit/(:num)/process'] = 'SPPD/process_edit/$1';

$route['surat'] = 'Surat';
$route['surat/process'] = 'Surat/process_surat';

$route['config/wablas'] = 'Config/WABlas';
$route['config/wablas/update'] = 'Config/WABlas/process_update';

$route['document/download/(:any)'] = 'Surat/download_surat/$1';

$route['penugasan'] = 'Penugasan';
$route['penugasan/add'] = 'Penugasan/add';
$route['penugasan/add/process'] = 'Penugasan/process_add';
$route['penugasan/edit/(:num)'] = 'Penugasan/edit/$1';
$route['penugasan/edit/(:num)/process'] = 'Penugasan/process_edit/$1';
$route['penugasan/delete'] = 'Penugasan/process_delete';
$route['penugasan/release'] = 'Penugasan/process_release';
$route['penugasan/collect'] = 'Penugasan/collect';
$route['penugasan/collect/process'] = 'Penugasan/process_collect';
$route['penugasan/verify'] = 'Penugasan/process_verify';
$route['penugasan/reject'] = 'Penugasan/process_reject';
$route['penugasan/reject/process'] = 'Penugasan/process_reject_task';
$route['penugasan/history'] = 'Penugasan/history';
$route['penugasan/process'] = 'Penugasan/process_task';
$route['penugasan/detail/(:num)'] = 'Penugasan/detail/$1';
$route['penugasan/detail/(:num)/delete'] = 'Penugasan/process_delete';
$route['penugasan/detail_status'] = 'Penugasan/detail_status';
$route['penugasan/switch'] = 'Penugasan/switch_task';
$route['penugasan/switch/process'] = 'Penugasan/process_switch_task';
$route['penugasan/villages'] = 'Penugasan/select_villages';

$route['penugasan/(:any)'] = 'Penugasan/index/$1';
$route['penugasan/(:any)/datatables'] = 'Penugasan/datatables/$1';
$route['penugasan/(:any)/add'] = 'Penugasan/add/$1';
$route['penugasan/(:any)/add/process'] = 'Penugasan/process_add/$1';
$route['penugasan/(:any)/edit/(:num)'] = 'Penugasan/edit/$1/$2';
$route['penugasan/(:any)/edit/(:num)/process'] = 'Penugasan/process_edit/$1/$2';
$route['penugasan/(:any)/delete'] = 'Penugasan/process_delete/$1';
$route['penugasan/(:any)/release'] = 'Penugasan/process_release/$1';
$route['penugasan/(:any)/collect'] = 'Penugasan/collect/$1';
$route['penugasan/(:any)/collect/process'] = 'Penugasan/process_collect/$1';
$route['penugasan/(:any)/verify'] = 'Penugasan/process_verify/$1';
$route['penugasan/(:any)/reject'] = 'Penugasan/process_reject/$1';
$route['penugasan/(:any)/reject/process'] = 'Penugasan/process_reject_task/$1';
$route['penugasan/(:any)/detail/(:num)'] = 'Penugasan/detail/$1/$2';
$route['penugasan/(:any)/detail/(:num)/delete'] = 'Penugasan/process_delete/$1';
$route['penugasan/(:any)/detail_status'] = 'Penugasan/detail_status/$1';
$route['penugasan/(:any)/switch'] = 'Penugasan/switch_task/$1';
$route['penugasan/(:any)/switch/process'] = 'Penugasan/process_switch_task/$1';
$route['penugasan/(:any)/history'] = 'Penugasan/history/$1';
$route['penugasan/(:any)/process'] = 'Penugasan/process_task';

$route['integration'] = 'Integration';
$route['integration/update'] = 'Integration/process_update';

$route['laporan/absensi'] = 'Laporan/Absensi';

$route['event'] = 'Event';
$route['event/add'] = 'Event/add';
$route['event/add/process'] = 'Event/process_add';
$route['event/delete'] = 'Event/process_delete';
$route['event/edit/(:num)'] = 'Event/edit/$1';
$route['event/edit/(:num)/process'] = 'Event/process_edit/$1';
$route['event/show_qr_code'] = 'Event/show_qr_code';
$route['event/show_qr_code/(:num)/print'] = 'Event/print_qr_code/$1';
$route['event/list_of_attendance'] = 'Event/list_of_attendance';
$route['event/list_of_attendance/print/(:num)'] = 'Event/print_list_of_attendance/$1';

$route['history/deletes'] = 'History/deletes';

$route['api/listofattendance/scan'] = 'API/ListOfAttendance/scan';
$route['api/listofattendance/process'] = 'API/ListOfAttendance/process';

$route['api/task/get'] = 'API/Tasks/get';

$route['cronjobs/sync/bpd'] = 'Cronjobs/sync_bpd';
$route['cronjobs/sync/aparatdesa'] = 'Cronjobs/sync_aparatdesa';
$route['cronjobs/sync/task/bpd'] = 'Cronjobs/sync_task_bpd';
$route['cronjobs/sync/task/aparatdesa'] = 'Cronjobs/sync_task_aparatdesa';

$route['cronjobs/task/delete'] = 'Cronjobs/delete_task';
