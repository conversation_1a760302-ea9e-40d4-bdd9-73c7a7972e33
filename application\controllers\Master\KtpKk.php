<?php

use Dompdf\Dompdf;
use Dompdf\Options;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property KtpKks $ktpkks
 * @property Superadmins $admins
 */
class KtpKk extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('KtpKks', 'ktpkks');
        $this->load->model('Superadmins', 'admins');
    }

    public function index()
    {
        $tahun = getGet('tahun');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($tahun != null) {
            $where['YEAR(a.tanggal_dikeluarkan)'] = $tahun;
        }

        $data = array();
        $data['title'] = 'Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga';
        $data['content'] = 'master/ktpkk/index';
        $data['tahun'] = $tahun;

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $data['ktpkk'] = $this->ktpkks->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        return $this->load->view('master', $data);
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Get filter parameters
        $tahun = getGet('tahun');

        $where = array();

        if ($tahun != null) {
            $where['YEAR(a.tanggal_dikeluarkan)'] = $tahun;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $ktpkk = $this->ktpkks->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/ktpkk/export', array(
            'ktpkk' => $ktpkk,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $this->ktpkks->getStatistics(getCurrentUser()->id ?? null)
        ), true));
        $dompdf->render();
        $dompdf->stream('Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga.pdf', array("Attachment" => false));
    }
}
