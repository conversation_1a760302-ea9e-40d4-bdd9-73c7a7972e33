<?php

use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property KtpKks $ktpkks
 * @property Superadmins $admins
 */
class KtpKk extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('KtpKks', 'ktpkks');
        $this->load->model('Superadmins', 'admins');
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $ktpkk = $this->ktpkks->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        $html2pdf = new Html2Pdf('L');
        $html2pdf->writeHTML($this->load->view('master/ktpkk/export', array(
            'ktpkk' => $ktpkk,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $this->ktpkks->getStatistics(getCurrentUser()->id ?? null)
        ), true));
        $html2pdf->output('Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga.pdf');
    }
}
