<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Ekspedisis extends MY_Model
{
    protected $table = 'ekspedisi';

    /**
     * Get statistics for ekspedisi data
     */
    public function getStatistics($userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        // Total ekspedisi
        $this->db->select('COUNT(*) as total_ekspedisi');
        $this->db->where($where);
        $totals = $this->db->get($this->table)->row();

        // By month (current year)
        $this->db->select('MONTH(tanggal_pengiriman) as bulan, COUNT(*) as jumlah');
        $this->db->where($where);
        $this->db->where('YEAR(tanggal_pengiriman)', date('Y'));
        $this->db->group_by('MONTH(tanggal_pengiriman)');
        $this->db->order_by('bulan', 'ASC');
        $by_month = $this->db->get($this->table)->result();

        // By ditujukan kepada (top 10)
        $this->db->select('ditujukan_kepada, COUNT(*) as jumlah');
        $this->db->where($where);
        $this->db->where('ditujukan_kepada IS NOT NULL');
        $this->db->where('ditujukan_kepada !=', '');
        $this->db->group_by('ditujukan_kepada');
        $this->db->order_by('jumlah', 'DESC');
        $this->db->limit(10);
        $by_tujuan = $this->db->get($this->table)->result();

        // Recent ekspedisi
        $this->db->select('tanggal_pengiriman, COUNT(*) as jumlah');
        $this->db->where($where);
        $this->db->where('tanggal_pengiriman >=', date('Y-m-d', strtotime('-30 days')));
        $this->db->group_by('tanggal_pengiriman');
        $this->db->order_by('tanggal_pengiriman', 'DESC');
        $this->db->limit(7);
        $recent_activity = $this->db->get($this->table)->result();

        return [
            'totals' => $totals,
            'by_month' => $by_month,
            'by_tujuan' => $by_tujuan,
            'recent_activity' => $recent_activity
        ];
    }

    /**
     * Search ekspedisi by nomor surat, isi singkat, or ditujukan kepada
     */
    public function search($keyword, $userid = null, $limit = 10)
    {
        $this->db->group_start();
        $this->db->like('nomor_surat', $keyword);
        $this->db->or_like('isi_singkat_surat', $keyword);
        $this->db->or_like('ditujukan_kepada', $keyword);
        $this->db->group_end();

        if ($userid) {
            $this->db->where('userid', $userid);
        }

        $this->db->limit($limit);
        $this->db->order_by('nomor_urut', 'ASC');

        return $this->db->get($this->table)->result();
    }

    /**
     * Get next nomor urut
     */
    public function getNextNomorUrut($userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        $this->db->select('MAX(nomor_urut) as max_nomor');
        $this->db->where($where);
        $result = $this->db->get($this->table)->row();

        return ($result->max_nomor ?? 0) + 1;
    }

    /**
     * Calculate auto fields before save
     */
    public function calculateAutoFields($data)
    {
        // Auto generate nomor urut if not provided
        if (empty($data['nomor_urut'])) {
            $data['nomor_urut'] = $this->getNextNomorUrut($data['userid'] ?? null);
        }

        return $data;
    }

    /**
     * Override insert to calculate auto fields
     */
    public function insert($data = [])
    {
        $data = $this->calculateAutoFields($data);
        return parent::insert($data);
    }

    /**
     * Override update to calculate auto fields
     */
    public function update($data = [], $where = [])
    {
        $data = $this->calculateAutoFields($data);
        return parent::update($data, $where);
    }

    /**
     * Get ekspedisi by date range
     */
    public function getByDateRange($start_date, $end_date, $userid = null)
    {
        $where = [
            'tanggal_pengiriman >=' => $start_date,
            'tanggal_pengiriman <=' => $end_date
        ];

        if ($userid) {
            $where['userid'] = $userid;
        }

        return $this->result($where);
    }

    /**
     * Get ekspedisi by tujuan
     */
    public function getByTujuan($tujuan, $userid = null)
    {
        $where = ['ditujukan_kepada' => $tujuan];
        if ($userid) {
            $where['userid'] = $userid;
        }

        return $this->result($where);
    }

    /**
     * Export data to array for Excel
     */
    public function exportToArray($where = [])
    {
        $data = $this->result($where);
        $export_data = [];

        foreach ($data as $row) {
            $export_data[] = [
                'No. Urut' => $row->nomor_urut,
                'Tanggal Pengiriman' => $row->tanggal_pengiriman,
                'Tanggal Surat' => $row->tanggal_surat,
                'Nomor Surat' => $row->nomor_surat,
                'Isi Singkat Surat' => $row->isi_singkat_surat,
                'Ditujukan Kepada' => $row->ditujukan_kepada,
                'Keterangan' => $row->keterangan
            ];
        }

        return $export_data;
    }
}
