<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Agendas extends MY_Model
{
    protected $table = 'agenda';

    /**
     * Get next nomor urut for new record
     */
    public function getNextNomorUrut($userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        $result = $this->db->select('MAX(nomor_urut) as max_nomor')
            ->where($where)
            ->get($this->table)
            ->row();

        return ($result && $result->max_nomor) ? $result->max_nomor + 1 : 1;
    }

    /**
     * Calculate auto fields before save
     */
    public function calculateAutoFields($data)
    {
        // Auto generate nomor urut if not provided
        if (empty($data['nomor_urut'])) {
            $data['nomor_urut'] = $this->getNextNomorUrut($data['userid'] ?? null);
        }

        return $data;
    }

    /**
     * Override insert to calculate auto fields
     */
    public function insert($data = [])
    {
        $data = $this->calculateAutoFields($data);
        return parent::insert($data);
    }

    /**
     * Override update to calculate auto fields
     */
    public function update($data = [], $where = [])
    {
        $data = $this->calculateAutoFields($data);
        return parent::update($data, $where);
    }

    /**
     * Get jenis agenda options
     */
    public function getJenisOptions()
    {
        return [
            'Surat Masuk' => 'Surat Masuk',
            'Surat Keluar' => 'Surat Keluar'
        ];
    }

    /**
     * Get statistics by jenis
     */
    public function getStatistics($userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        $total = $this->db->where($where)->count_all_results($this->table);

        $surat_masuk = $this->db->where($where)->where('jenis', 'Surat Masuk')->count_all_results($this->table);

        $surat_keluar = $this->db->where($where)->where('jenis', 'Surat Keluar')->count_all_results($this->table);

        return [
            'total' => $total,
            'surat_masuk' => $surat_masuk,
            'surat_keluar' => $surat_keluar
        ];
    }

    /**
     * Search agenda with filters
     */
    public function search($params = [])
    {
        $this->db->select('*');
        $this->db->from($this->table);

        if (!empty($params['jenis'])) {
            $this->db->where('jenis', $params['jenis']);
        }

        if (!empty($params['start_date'])) {
            $this->db->where('tanggal >=', $params['start_date']);
        }

        if (!empty($params['end_date'])) {
            $this->db->where('tanggal <=', $params['end_date']);
        }

        if (!empty($params['nomor_surat'])) {
            $this->db->like('nomor_surat', $params['nomor_surat']);
        }

        if (!empty($params['perihal'])) {
            $this->db->like('perihal', $params['perihal']);
        }

        if (!empty($params['userid'])) {
            $this->db->where('userid', $params['userid']);
        }

        $this->db->order_by('tanggal', 'DESC');
        $this->db->order_by('id', 'DESC');

        return $this->db->get();
    }

    /**
     * Get agenda by jenis
     */
    public function getByJenis($jenis, $userid = null)
    {
        $where = ['jenis' => $jenis];
        if ($userid) {
            $where['userid'] = $userid;
        }

        return $this->db->where($where)
            ->order_by('tanggal', 'DESC')
            ->order_by('id', 'DESC')
            ->get($this->table);
    }

    /**
     * Get agenda by date range
     */
    public function getByDateRange($start_date, $end_date, $userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        return $this->db->where($where)
            ->where('tanggal >=', $start_date)
            ->where('tanggal <=', $end_date)
            ->order_by('tanggal', 'DESC')
            ->order_by('id', 'DESC')
            ->get($this->table);
    }

    /**
     * Validate nomor surat uniqueness
     */
    public function isNomorSuratExists($nomor_surat, $jenis, $exclude_id = null)
    {
        $this->db->where('nomor_surat', $nomor_surat);
        $this->db->where('jenis', $jenis);

        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }

        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Export to array for Excel/PDF
     */
    public function exportToArray($params = [])
    {
        $query = $this->search($params);
        $results = $query->result_array();

        $export_data = [];
        $no = 1;

        foreach ($results as $row) {
            $export_data[] = [
                'No' => $no++,
                'Tanggal' => date('d/m/Y', strtotime($row['tanggal'])),
                'Jenis' => $row['jenis'],
                'Nomor Surat' => $row['nomor_surat'],
                'Asal' => $row['asal'],
                'Tujuan' => $row['tujuan'],
                'Perihal' => $row['perihal'],
                'Keterangan' => $row['keterangan']
            ];
        }

        return $export_data;
    }
}
