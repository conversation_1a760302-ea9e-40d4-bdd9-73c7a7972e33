# Dokumentasi Lembaran & Berita Desa - Unified Feature (Updated)

## Konsep Fitur

**Lembaran & Berita Desa** adalah **SATU FITUR** yang menampilkan data dari tabel `lembarandesa` dengan filter berdasarkan `jenis`:

- **Lembaran Desa** = `jenis = 'Lembaran Desa'`
- **Berita Desa** = `jenis = 'Berita Desa'`

## Field yang Digunakan (Sesuai Permintaan User)

### Field yang DIHAPUS:
- ❌ `uraian_singkat` - Dihapus
- ❌ `nomor_surat_pengantar` - Dihapus
- ❌ `tanggal_pelaporan_bupati` - Dihapus
- ❌ `keterangan_tambahan` - Dihapus

### Field yang DIUBAH:
- ✅ `keterangan` - Diubah dari SELECT menjadi TEXTAREA

### Field yang DITAMBAHKAN:
- ✅ `tanggal_diundangkan` - Input tanggal diundangkan
- ✅ `nomor_diundangkan` - Input nomor diundangkan

## Perubahan yang <PERSON>ukan

### 1. **Controller LembaranDesa** ✅
File: `application/controllers/Master/LembaranDesa.php`

**Method yang ditambahkan:**
- `index()` - Halaman utama dengan filter jenis, tanggal
- Updated `export()` - Support filter jenis dan tanggal

**Fitur:**
- Filter berdasarkan jenis (Semua, Lembaran Desa, Berita Desa)
- Filter berdasarkan tanggal (sesuai jenis yang dipilih)
- User role filtering (BPD/Villages)
- Export PDF dengan filter

### 2. **View Index Page** ✅
File: `application/views/master/lembarandesa/index.php`

**Fitur yang ditambahkan:**
- Filter jenis (dropdown: Semua, Lembaran Desa, Berita Desa)
- Filter tanggal (Tanggal Mulai & Tanggal Selesai)
- Tombol Filter, Reset, dan Export
- Tabel unified dengan kolom yang sesuai untuk kedua jenis
- Badge untuk membedakan jenis data
- Action buttons (Edit, Delete) untuk BPD

### 3. **Routing** ✅
File: `application/config/routes.php`

**Route yang digunakan:**
```php
// Lembaran & Berita Desa routes (satu fitur)
$route['master/lembarandesa'] = 'Master/LembaranDesa/index';
$route['master/lembarandesa/export'] = 'Master/LembaranDesa/export';
```

## Struktur Halaman Index

### Filter Section
```html
<form action="/master/lembarandesa" method="GET">
    <select name="jenis">
        <option value="all">Semua</option>
        <option value="lembaran">Lembaran Desa</option>
        <option value="berita">Berita Desa</option>
    </select>
    <input type="date" name="startdate" />
    <input type="date" name="enddate" />
    <button type="submit">Filter</button>
    <a href="/master/lembarandesa">Reset</a>
    <button onclick="exportData()">Export</button>
</form>
```

### Tabel Unified
**Kolom yang ditampilkan:**
1. No. Urut
2. Jenis (Badge: Lembaran Desa/Berita Desa)
3. Nomor Ditetapkan
4. Tanggal Ditetapkan
5. Tentang
6. Nomor Lembaran/Berita (conditional)
7. Tanggal Lembaran/Berita (conditional)
8. Status
9. Dibuat Oleh
10. Action (Edit, Delete) - hanya untuk BPD

### Logic Conditional Display
- **Kolom Nomor Lembaran/Berita**: 
  - Jika Peraturan Desa → tampilkan `nomor_lembaran_desa`
  - Jika Peraturan Kepala Desa/Bersama → tampilkan `nomor_berita_desa`
- **Kolom Tanggal Lembaran/Berita**:
  - Jika Peraturan Desa → tampilkan `tanggal_lembaran_desa`
  - Jika Peraturan Kepala Desa/Bersama → tampilkan `tanggal_berita_desa`

### JavaScript Functions
- `exportData()` - Export PDF dengan filter jenis dan tanggal
- `deleteData(url)` - Konfirmasi dan hapus data via AJAX

## Cara Penggunaan

### 1. **Akses Halaman Index**
- URL: `/master/lembarandesa`
- Menampilkan semua data (Lembaran & Berita Desa)

### 2. **Filter Data**
- **Filter Jenis**: Pilih Semua/Lembaran Desa/Berita Desa
- **Filter Tanggal**: Pilih tanggal mulai dan/atau tanggal selesai
- Klik tombol "Filter"
- Data akan difilter sesuai kriteria

### 3. **Reset Filter**
- Klik tombol "Reset" untuk menghapus semua filter
- Kembali menampilkan semua data

### 4. **Export PDF**
- Klik tombol "Export"
- PDF akan di-generate dengan data sesuai filter yang aktif
- Jika tidak ada filter, akan export semua data

### 5. **Tambah Data**
- Klik tombol "Tambah" (redirect ke form peraturan desa)
- Pilih jenis peraturan yang sesuai
- Data akan masuk ke kategori yang tepat

### 6. **Edit/Delete Data**
- Edit: Redirect ke form edit peraturan desa
- Delete: Konfirmasi dan hapus via AJAX

## Filter Logic

### Filter Jenis
```php
if ($jenis === 'lembaran') {
    $where['a.jenis_peraturan'] = 'Peraturan Desa';
    $date_field = 'tanggal_lembaran_desa';
} elseif ($jenis === 'berita') {
    $this->peraturandesas->where_in('a.jenis_peraturan', ['Peraturan Kepala Desa', 'Peraturan Bersama Kepala Desa']);
    $date_field = 'tanggal_berita_desa';
} else {
    // Show all types
    $this->peraturandesas->where_in('a.jenis_peraturan', ['Peraturan Desa', 'Peraturan Kepala Desa', 'Peraturan Bersama Kepala Desa']);
    $date_field = 'date';
}
```

### Filter Tanggal
- **Lembaran Desa**: Filter berdasarkan `tanggal_lembaran_desa`
- **Berita Desa**: Filter berdasarkan `tanggal_berita_desa`
- **Semua**: Filter berdasarkan `date` (tanggal ditetapkan)

## Keamanan & Akses

### Permission Level
- **BPD**: Full access (view, add, edit, delete, export)
- **Villages**: View dan export saja
- **Guest**: Tidak ada akses

### User Filtering
- **BPD/Villages**: Hanya melihat data dari BPD di desa yang sama
- **Admin**: Melihat semua data

## Integrasi dengan Sistem Existing

### Data Source
- Menggunakan tabel `lembarandesa` yang sudah ada
- Filter berdasarkan `jenis` untuk membedakan Lembaran vs Berita
- Join dengan `msusers` untuk informasi pembuat

### Form Integration
- Add/Edit menggunakan form khusus untuk lembaran/berita desa
- Field `jenis` menentukan kategori (Lembaran Desa/Berita Desa)
- Struktur tabel sesuai dengan kebutuhan lembaran dan berita desa

### Export Integration
- Menggunakan view export yang sudah ada
- Support filter jenis dan tanggal
- PDF title sesuai dengan filter yang aktif

## Badge System

### Jenis Badge
- **Lembaran Desa**: `badge-primary` (biru)
- **Berita Desa**: `badge-success` (hijau)
- **Lainnya**: `badge-secondary` (abu-abu)

### Status Badge
- **Berlaku**: `badge-success` (hijau)
- **Diubah**: `badge-warning` (kuning)
- **Dicabut**: `badge-danger` (merah)

## Testing Checklist

### Functionality Tests
- [ ] Halaman index dapat diakses
- [ ] Filter jenis berfungsi (Semua, Lembaran, Berita)
- [ ] Filter tanggal berfungsi sesuai jenis
- [ ] Tombol reset menghapus semua filter
- [ ] Export PDF dengan/tanpa filter
- [ ] Edit data (redirect ke form peraturan desa)
- [ ] Hapus data via AJAX
- [ ] Conditional display kolom Lembaran/Berita

### Permission Tests
- [ ] BPD dapat akses semua fitur
- [ ] Villages hanya dapat view dan export
- [ ] User filtering berdasarkan desa

### UI/UX Tests
- [ ] Badge jenis dan status tampil benar
- [ ] Responsive design
- [ ] Form validation
- [ ] AJAX delete confirmation
- [ ] Breadcrumb navigation

## Update Terbaru - Form CRUD Lengkap

### Struktur Tabel Final:
```
| No.Urut | Jenis | No.Ditetapkan | Tgl.Ditetapkan | Tentang | No.Diundangkan | Tgl.Diundangkan | Keterangan | Dibuat | Action |
```

### Field Mapping Final:
- **No.Urut**: Auto increment (key + 1)
- **Jenis**: Badge system (Lembaran Desa = Primary, Berita Desa = Success)
- **No.Ditetapkan**: `nomor_ditetapkan` (required)
- **Tgl.Ditetapkan**: `tanggal_ditetapkan` (required, format dd/mm/yyyy)
- **Tentang**: `tentang` (required)
- **No.Diundangkan**: `nomor_diundangkan` (optional)
- **Tgl.Diundangkan**: `tanggal_diundangkan` (optional, format dd/mm/yyyy)
- **Keterangan**: `keterangan` (textarea, optional)
- **Dibuat**: `createdname` atau `createdusername`
- **Action**: Edit/Delete buttons (hanya untuk BPD)

### Form Fields (Sesuai Permintaan User):
1. **Jenis** - Dropdown (Lembaran Desa/Berita Desa) - Required
2. **Nomor Ditetapkan** - Text input - Required
3. **Tanggal Ditetapkan** - Date input - Required
4. **Tentang** - Text input - Required
5. **Nomor Diundangkan** - Text input - Optional ✅ DITAMBAHKAN
6. **Tanggal Diundangkan** - Date input - Optional ✅ DITAMBAHKAN
7. **Keterangan** - Textarea (4 rows) - Optional ✅ DIUBAH dari SELECT ke TEXTAREA

### Field yang DIHAPUS (sesuai permintaan):
- ❌ `uraian_singkat` - Dihapus
- ❌ `nomor_surat_pengantar` - Dihapus
- ❌ `tanggal_pelaporan_bupati` - Dihapus
- ❌ `keterangan_tambahan` - Dihapus

### CRUD Operations:
- ✅ **Create**: `/master/lembarandesa/add`
- ✅ **Read**: `/master/lembarandesa` (index dengan filter)
- ✅ **Update**: `/master/lembarandesa/edit/{id}`
- ✅ **Delete**: `/master/lembarandesa/delete/{id}`
- ✅ **Export**: `/master/lembarandesa/export`

### Access Control:
- **View**: Semua user yang login
- **Add/Edit/Delete**: Hanya BPD

## Catatan Penting (Updated)

1. **Unified Feature**: Lembaran & Berita Desa adalah SATU fitur, bukan dua fitur terpisah
2. **Data Source**: Menggunakan tabel `lembarandesa` (bukan peraturandesa)
3. **Form Khusus**: Memiliki form add/edit sendiri (bukan menggunakan form peraturandesa)
4. **Field Customization**: Field disesuaikan dengan permintaan user
5. **Export**: Support filter jenis dan tanggal dalam satu export
6. **No CrudCore**: Halaman index tidak bergantung pada CrudCore
7. **Complete CRUD**: Fitur CRUD lengkap dengan validasi dan access control

## Update Export PDF - Struktur Tabel Baru

### Struktur Tabel Export PDF:
```
|NOMOR|JENIS PERATURAN|NOMOR DAN TANGGAL|TENTANG|    DIUNDANGKAN    |KET|
|URUT |   DI DESA     |   DITETAPKAN    |       |TANGGAL|NOMOR      |   |
|-----|---------------|-----------------|-------|-------|-----------|---|
|  1  |Lembaran Desa  |001/PD/2024      |Tentang| 01/01 |001/LD/24  | - |
|     |               |01/01/2024       |APBDes | /2024 |           |   |
|  2  |Berita Desa    |002/PKD/2024     |Tentang| 02/01 |002/BD/24  | - |
|     |               |02/01/2024       |Bantuan| /2024 |           |   |
```

### Kolom Export PDF:
1. **NOMOR URUT** (20mm) - Auto increment
2. **JENIS PERATURAN DI DESA** (35mm) - Lembaran Desa/Berita Desa
3. **NOMOR DAN TANGGAL DITETAPKAN** (45mm) - Nomor + tanggal dalam satu kolom
4. **TENTANG** (60mm) - Isi tentang peraturan
5. **DIUNDANGKAN** - Header dengan 2 sub-kolom:
   - **TANGGAL** (45mm) - Tanggal diundangkan
   - **NOMOR** (35mm) - Nomor diundangkan
6. **KET** (33mm) - Keterangan

### Format Data Export:
- **Jenis**: Wordwrap 15 karakter per baris
- **Nomor & Tanggal Ditetapkan**: Nomor di baris pertama, tanggal di baris kedua
- **Tentang**: Wordwrap 25 karakter per baris
- **Tanggal**: Format Indonesia (dd/mm/yyyy)
- **Nomor Diundangkan**: Wordwrap 15 karakter per baris
- **Keterangan**: Wordwrap 12 karakter per baris

### Layout PDF:
- **Format**: A4 Landscape (273mm total width)
- **Header**: Logo BPD + Judul "BUKU LEMBARAN DESA & BERITA DESA"
- **Tabel**: Struktur dengan rowspan dan colspan untuk header
- **Footer**: Tanda tangan Ketua BPD dan Sekretaris BPD

Sekarang fitur **"Lembaran & Berita Desa"** sudah lengkap dengan CRUD operations dan export PDF yang sesuai dengan format yang diminta! 🎉
