<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MutasiPenduduks extends MY_Model
{
    protected $table = 'mutasipenduduk';

    /**
     * Get statistics for mutasi penduduk data
     */
    public function getStatistics($userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        // Total mutasi
        $this->db->select('COUNT(*) as total_mutasi');
        $this->db->where($where);
        $totals = $this->db->get($this->table)->row();

        // By jenis kelamin
        $this->db->select('jenis_kelamin, COUNT(*) as jumlah');
        $this->db->where($where);
        $this->db->group_by('jenis_kelamin');
        $this->db->order_by('jumlah', 'DESC');
        $by_gender = $this->db->get($this->table)->result();

        // Penambahan (Datang)
        $this->db->select('COUNT(*) as total_datang');
        $this->db->where($where);
        $this->db->where('datang_dari IS NOT NULL');
        $this->db->where('datang_dari !=', '');
        $penambahan = $this->db->get($this->table)->row();

        // Pengurangan (Pindah)
        $this->db->select('COUNT(*) as total_pindah');
        $this->db->where($where);
        $this->db->where('pindah_ke IS NOT NULL');
        $this->db->where('pindah_ke !=', '');
        $pengurangan_pindah = $this->db->get($this->table)->row();

        // Pengurangan (Meninggal)
        $this->db->select('COUNT(*) as total_meninggal');
        $this->db->where($where);
        $this->db->where('meninggal', 'Ya');
        $pengurangan_meninggal = $this->db->get($this->table)->row();

        // By month (current year)
        $this->db->select('
            MONTH(COALESCE(tanggal_datang, tanggal_pindah, tanggal_meninggal)) as bulan,
            COUNT(*) as jumlah
        ');
        $this->db->where($where);
        $this->db->where('YEAR(COALESCE(tanggal_datang, tanggal_pindah, tanggal_meninggal)) =', date('Y'));
        $this->db->group_by('bulan');
        $this->db->order_by('bulan', 'ASC');
        $by_month = $this->db->get($this->table)->result();

        // Recent activity (last 7 days)
        $this->db->select('
            DATE(COALESCE(tanggal_datang, tanggal_pindah, tanggal_meninggal)) as tanggal,
            COUNT(*) as jumlah
        ');
        $this->db->where($where);
        $this->db->where('COALESCE(tanggal_datang, tanggal_pindah, tanggal_meninggal) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)');
        $this->db->group_by('tanggal');
        $this->db->order_by('tanggal', 'DESC');
        $recent_activity = $this->db->get($this->table)->result();

        // By kewarganegaraan
        $this->db->select('kewarganegaraan, COUNT(*) as jumlah');
        $this->db->where($where);
        $this->db->group_by('kewarganegaraan');
        $by_kewarganegaraan = $this->db->get($this->table)->result();

        return [
            'totals' => $totals,
            'penambahan' => $penambahan,
            'pengurangan_pindah' => $pengurangan_pindah,
            'pengurangan_meninggal' => $pengurangan_meninggal,
            'by_gender' => $by_gender,
            'by_month' => $by_month,
            'recent_activity' => $recent_activity,
            'by_kewarganegaraan' => $by_kewarganegaraan
        ];
    }

    /**
     * Search mutasi by nama
     */
    public function search($keyword, $userid = null, $limit = 10)
    {
        $this->db->group_start();
        $this->db->like('nama_lengkap', $keyword);
        $this->db->or_like('datang_dari', $keyword);
        $this->db->or_like('pindah_ke', $keyword);
        $this->db->or_like('tempat_lahir', $keyword);
        $this->db->group_end();

        if ($userid) {
            $this->db->where('userid', $userid);
        }

        $this->db->limit($limit);
        $this->db->order_by('nomor_urut', 'ASC');

        return $this->db->get($this->table)->result();
    }

    /**
     * Get next nomor urut
     */
    public function getNextNomorUrut($userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        $this->db->select('MAX(nomor_urut) as max_nomor');
        $this->db->where($where);
        $result = $this->db->get($this->table)->row();

        return ($result->max_nomor ?? 0) + 1;
    }

    /**
     * Get mutasi by type (datang, pindah, meninggal)
     */
    public function getByType($type, $userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        switch ($type) {
            case 'datang':
                $this->db->where('datang_dari IS NOT NULL');
                $this->db->where('datang_dari !=', '');
                break;
            case 'pindah':
                $this->db->where('pindah_ke IS NOT NULL');
                $this->db->where('pindah_ke !=', '');
                break;
            case 'meninggal':
                $this->db->where('meninggal', 'Ya');
                break;
        }

        $this->db->where($where);
        return $this->db->get($this->table)->result();
    }

    /**
     * Get mutasi by date range
     */
    public function getByDateRange($start_date, $end_date, $userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        $this->db->where($where);
        $this->db->group_start();
        $this->db->where('tanggal_datang BETWEEN', $start_date . ' AND ' . $end_date);
        $this->db->or_where('tanggal_pindah BETWEEN', $start_date . ' AND ' . $end_date);
        $this->db->or_where('tanggal_meninggal BETWEEN', $start_date . ' AND ' . $end_date);
        $this->db->group_end();

        return $this->db->get($this->table)->result();
    }

    /**
     * Export data to array for Excel
     */
    public function exportToArray($where = [])
    {
        $data = $this->result($where);
        $export_data = [];

        foreach ($data as $row) {
            $export_data[] = [
                'No. Urut' => $row->nomor_urut,
                'Nama Lengkap' => $row->nama_lengkap,
                'Tempat Lahir' => $row->tempat_lahir,
                'Tanggal Lahir' => $row->tanggal_lahir,
                'Jenis Kelamin' => $row->jenis_kelamin,
                'Kewarganegaraan' => $row->kewarganegaraan,
                'Datang Dari' => $row->datang_dari,
                'Tanggal Datang' => $row->tanggal_datang,
                'Pindah Ke' => $row->pindah_ke,
                'Tanggal Pindah' => $row->tanggal_pindah,
                'Meninggal' => $row->meninggal,
                'Tanggal Meninggal' => $row->tanggal_meninggal,
                'Keterangan' => $row->keterangan
            ];
        }

        return $export_data;
    }
}
