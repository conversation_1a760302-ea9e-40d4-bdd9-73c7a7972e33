<?php

use Dompdf\Dompdf;
use Dompdf\Options;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Lembarandesas $lembarandesas
 * @property Superadmins $admins
 * @property CI_Form_validation $form_validation
 * @property CI_Input $input
 */
class LembaranDesa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->model('Lembarandesas', 'lembarandesas');
        $this->load->model('Peraturandesas', 'peraturandesas');
        $this->load->model('Superadmins', 'admins');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');
        $jenis = getGet('jenis') ?: 'all'; // all, lembaran, berita

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        // Filter berdasarkan jenis
        if ($jenis === 'lembaran') {
            $where['a.jenis'] = 'Lembaran Desa';
        } elseif ($jenis === 'berita') {
            $where['a.jenis'] = 'Berita Desa';
        }
        // Jika 'all', tidak ada filter jenis

        // Filter tanggal
        if ($startdate != null) {
            $where['DATE(a.tanggal_diundangkan) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_diundangkan) <='] = $enddate;
        }

        // User filtering
        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();

            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }

            if (!empty($bpdid)) {
                $this->lembarandesas->where_in('userid', $bpdid);
            }
        }

        $data = array();
        $data['title'] = 'Lembaran & Berita Desa';
        $data['content'] = 'master/lembarandesa/index';
        $data['items'] = $this->lembarandesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.tanggal_diundangkan', 'DESC')
            ->result($where);
        $data['startdate'] = $startdate;
        $data['enddate'] = $enddate;
        $data['jenis'] = $jenis;

        return $this->load->view('master', $data);
    }

    public function export()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');
        $jenis = getGet('jenis') ?: 'all';

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        // Filter berdasarkan jenis
        if ($jenis === 'lembaran') {
            $where['a.jenis'] = 'Lembaran Desa';
        } elseif ($jenis === 'berita') {
            $where['a.jenis'] = 'Berita Desa';
        }
        // Jika 'all', tidak ada filter jenis

        // Filter tanggal
        if ($startdate != null) {
            $where['DATE(a.tanggal_diundangkan) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_diundangkan) <='] = $enddate;
        }

        // User filtering
        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();

            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }

            if (!empty($bpdid)) {
                $this->lembarandesas->where_in('userid', $bpdid);
            }
        }

        $items = $this->lembarandesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.tanggal_diundangkan', 'DESC')
            ->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);
        $dompdf->setPaper('A4', 'landscape');

        $html = $this->load->view('master/lembarandesa/export', [
            'items' => $items,
            'user' => $this->admins->get(['id' => getCurrentIdUser()])->row(),
            'jenis' => $jenis,
        ], true);

        $dompdf->loadHtml($html);
        $dompdf->render();
        $dompdf->stream('Lembaran & Berita Desa.pdf', ["Attachment" => false]);
    }

    public function add()
    {
        if (!isLogin()) {
            redirect('auth/login');
        }

        if (!isBPD()) {
            show_error('Akses ditolak', 403);
        }

        $data = array();
        $data['title'] = 'Tambah Lembaran & Berita Desa';
        $data['content'] = 'master/lembarandesa/add';

        $this->load->view('master', $data);
    }

    public function edit($id)
    {
        if (!isLogin()) {
            redirect('auth/login');
        }

        if (!isBPD()) {
            show_error('Akses ditolak', 403);
        }

        $lembarandesa = $this->lembarandesas->get(['id' => $id])->row();
        if (!$lembarandesa) {
            show_404();
        }

        $data = array();
        $data['title'] = 'Edit Lembaran & Berita Desa';
        $data['content'] = 'master/lembarandesa/edit';
        $data['lembarandesa'] = $lembarandesa;

        $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $this->form_validation->set_rules('jenis', 'Jenis', 'required');
        $this->form_validation->set_rules('nomor_ditetapkan', 'Nomor Ditetapkan', 'required');
        $this->form_validation->set_rules('tanggal_ditetapkan', 'Tanggal Ditetapkan', 'required');
        $this->form_validation->set_rules('tentang', 'Tentang', 'required');

        if ($this->form_validation->run() == FALSE) {
            return JSONResponseDefault('FAILED', validation_errors());
        }

        $data = $this->input->post();

        // Set audit fields
        $data['userid'] = getCurrentUser()->id;
        $data['createdby'] = getCurrentUser()->id;
        $data['createddate'] = date('Y-m-d H:i:s');

        if ($this->lembarandesas->insert($data)) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil disimpan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menyimpan data');
        }
    }

    public function process_edit()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $this->form_validation->set_rules('jenis', 'Jenis', 'required');
        $this->form_validation->set_rules('nomor_ditetapkan', 'Nomor Ditetapkan', 'required');
        $this->form_validation->set_rules('tanggal_ditetapkan', 'Tanggal Ditetapkan', 'required');
        $this->form_validation->set_rules('tentang', 'Tentang', 'required');

        if ($this->form_validation->run() == FALSE) {
            return JSONResponseDefault('FAILED', validation_errors());
        }

        $id = $this->input->post('id');
        $data = $this->input->post();
        unset($data['id']);

        // Set audit fields
        $data['updatedby'] = getCurrentUser()->id;
        $data['updateddate'] = date('Y-m-d H:i:s');

        if ($this->lembarandesas->update(['id' => $id], $data)) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil diupdate');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengupdate data');
        }
    }

    public function delete($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        if ($this->lembarandesas->delete(['id' => $id])) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }
}
