<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item" aria-current="page">Buku Agenda</li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Buku Agenda</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4>Data Buku Agenda</h4>
                </div>

                <div>
                    <?php if (isBPD()): ?>
                        <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i>
                            <span>Tambah</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="" class="form-label">Tanggal Mulai</label>
                                <input type="date" name="startdate" class="form-control" value="<?= getGet('startdate') ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="" class="form-label">Tanggal Selesai</label>
                                <input type="date" name="enddate" class="form-control" value="<?= getGet('enddate') ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">Filter</button>

                                <a href="<?= base_url(uri_string()) ?>" class="btn btn-warning">Reset</a>

                                <button type="button" class="btn btn-danger" onclick="exportData()">Export</button>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Tanggal</th>
                                <th>Jenis</th>
                                <th>Nomor Surat</th>
                                <th>Asal</th>
                                <th>Tujuan</th>
                                <th>Perihal</th>
                                <th>Keterangan</th>
                                <th>Dokumen</th>
                                <?php if (isBPD()): ?>
                                    <th>Aksi</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $no = 1; ?>
                            <?php foreach ($agendas as $agenda): ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td><?= date('d/m/Y', strtotime($agenda->tanggal)) ?></td>
                                    <td>
                                        <span class="badge <?= $agenda->jenis == 'Surat Masuk' ? 'bg-primary' : 'bg-success' ?>">
                                            <?= $agenda->jenis ?>
                                        </span>
                                    </td>
                                    <td><?= $agenda->nomor_surat ?></td>
                                    <td><?= $agenda->asal ?: '-' ?></td>
                                    <td><?= $agenda->tujuan ?: '-' ?></td>
                                    <td><?= $agenda->perihal ?></td>
                                    <td><?= $agenda->keterangan ?: '-' ?></td>
                                    <td>
                                        <?php if ($agenda->document): ?>
                                            <a href="<?= base_url('uploads/' . $agenda->document) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fa fa-file"></i> Lihat
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php if (isBPD()): ?>
                                        <td>
                                            <a href="<?= base_url(uri_string() . '/edit/' . $agenda->id) ?>" class="btn btn-sm btn-primary">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteData('<?= base_url('master/bukuagenda/delete/' . $agenda->id) ?>')">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportData() {
        let startdate = $('input[name="startdate"]').val();
        let enddate = $('input[name="enddate"]').val();

        window.open(`<?= base_url(uri_string()) ?>/export?startdate=${startdate}&enddate=${enddate}`, '_blank');
    }

    function deleteData(url) {
        if (confirm('Apakah Anda yakin ingin menghapus data ini?')) {
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'OK') {
                        alert('Data berhasil dihapus');
                        location.reload();
                    } else {
                        alert('Gagal menghapus data: ' + response.message);
                    }
                },
                error: function() {
                    alert('Terjadi kesalahan saat menghapus data');
                }
            });
        }
    }
</script>