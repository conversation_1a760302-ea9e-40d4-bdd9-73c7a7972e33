# Dokumentasi Custom Buku Induk Penduduk

## Overview
Dokumentasi ini menjelaskan perubahan yang dilakukan untuk membuat custom index.php pada fitur Buku Induk Penduduk agar tidak bergantung pada CrudCore dan menambahkan filter tanggal serta tombol export.

## Perubahan yang Dilakukan

### 1. Controller: `application/controllers/Master/IndukPenduduk.php`

#### Method `index()` - Diperbarui
- **Sebelum**: Menggunakan CrudCore untuk menampilkan data
- **Sesudah**: Custom implementation dengan filter tanggal
- **Fitur Baru**:
  - Filter berdasarkan tanggal mulai (`startdate`)
  - Filter berdasarkan tanggal selesai (`enddate`)
  - Jo<PERSON> dengan tabel `msusers` untuk menampilkan pembuat data
  - User filtering (BPD/Villages hanya melihat data mereka sendiri)

```php
// Filter tanggal
if ($startdate != null) {
    $where['DATE(a.createddate) >='] = $startdate;
}
if ($enddate != null) {
    $where['DATE(a.createddate) <='] = $enddate;
}
```

#### Method `export()` - Diperbarui
- **Fitur Baru**: Mendukung filter tanggal pada export
- **Parameter**: Menerima `startdate` dan `enddate` dari GET parameter
- **Format**: Excel (.xlsx) dengan data yang sudah difilter

#### Method `delete()` - Diperbarui
- **Fitur Baru**: Mendukung parameter ID dari URL
- **Backward Compatible**: Tetap mendukung POST data untuk kompatibilitas

#### Constructor - Diperbarui
- **Library Baru**: Menambahkan `form_validation` library
- **DocBlock**: Menambahkan property annotations untuk IDE support

### 2. View: `application/views/master/indukpenduduk/index.php`

#### Struktur Baru
- **Filter Form**: Form dengan input tanggal mulai dan selesai
- **Button Actions**: Filter, Reset, Export
- **Table Enhancement**: Menambahkan kolom "Dibuat Oleh"
- **Access Control**: Tombol aksi hanya tampil untuk user BPD

#### Filter Form
```html
<form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
    <div class="row align-items-end">
        <div class="col-md-3">
            <div class="mb-3">
                <label for="" class="form-label">Tanggal Mulai</label>
                <input type="date" name="startdate" class="form-control" value="<?= $startdate ?>">
            </div>
        </div>
        <div class="col-md-3">
            <div class="mb-3">
                <label for="" class="form-label">Tanggal Selesai</label>
                <input type="date" name="enddate" class="form-control" value="<?= $enddate ?>">
            </div>
        </div>
        <div class="col-md-3">
            <div class="mb-3">
                <button type="submit" class="btn btn-primary">Filter</button>
                <a href="<?= base_url(uri_string()) ?>" class="btn btn-warning">Reset</a>
                <button type="button" class="btn btn-danger" onclick="exportData()">Export</button>
            </div>
        </div>
    </div>
</form>
```

#### JavaScript Functions
- **exportData()**: Function untuk export dengan filter tanggal
- **deleteData()**: Function untuk hapus data dengan konfirmasi

### 3. Routes: `application/config/routes.php`

#### Routes Baru
```php
$route['master/indukpenduduk'] = 'Master/IndukPenduduk/index';
$route['master/indukpenduduk/add'] = 'Master/IndukPenduduk/add';
$route['master/indukpenduduk/edit/(:num)'] = 'Master/IndukPenduduk/edit/$1';
$route['master/indukpenduduk/process_add'] = 'Master/IndukPenduduk/process_add';
$route['master/indukpenduduk/process_edit'] = 'Master/IndukPenduduk/process_edit';
$route['master/indukpenduduk/delete/(:num)'] = 'Master/IndukPenduduk/delete/$1';
$route['master/indukpenduduk/export'] = 'Master/IndukPenduduk/export';
$route['master/indukpenduduk/import'] = 'Master/IndukPenduduk/import';
$route['master/indukpenduduk/process_import'] = 'Master/IndukPenduduk/process_import';
$route['master/indukpenduduk/download_template'] = 'Master/IndukPenduduk/download_template';
$route['master/indukpenduduk/export_excel'] = 'Master/IndukPenduduk/export_excel';
```

## Fitur yang Ditambahkan

### 1. Filter Tanggal
- **Input**: Tanggal mulai dan tanggal selesai
- **Fungsi**: Filter data berdasarkan tanggal pembuatan (`createddate`)
- **SQL**: Menggunakan `DATE()` function untuk filter tanggal

### 2. Export dengan Filter
- **Format**: Excel (.xlsx)
- **Data**: Sesuai dengan filter yang diterapkan
- **Parameter**: Menggunakan GET parameter untuk `startdate` dan `enddate`

### 3. Access Control
- **BPD Users**: Dapat melihat, menambah, edit, hapus, dan export
- **Village Users**: Hanya dapat melihat data mereka sendiri
- **Button Visibility**: Tombol aksi hanya tampil untuk user BPD

### 4. Enhanced Table
- **Kolom Baru**: "Dibuat Oleh" menampilkan nama dan username pembuat
- **Data Join**: Join dengan tabel `msusers` untuk informasi user
- **Responsive**: Menggunakan class `datatables` untuk responsive table

## Pattern yang Diikuti

### 1. Referensi dari PeraturanDesa
- **Controller Pattern**: Mengikuti struktur `PeraturanDesa.php`
- **View Pattern**: Mengikuti layout `peraturandesa/index.php`
- **Filter Implementation**: Sama dengan implementasi di PeraturanDesa
- **Export Function**: Menggunakan pattern yang sama

### 2. URL Pattern
- **uri_string()**: Menggunakan helper untuk dynamic URL
- **RESTful Routes**: Mengikuti pattern RESTful untuk CRUD operations
- **Parameter Handling**: Konsisten dengan pattern aplikasi

### 3. JavaScript Pattern
- **Export Function**: Menggunakan `window.open()` dengan parameter
- **Delete Confirmation**: Menggunakan SweetAlert dengan pattern yang konsisten
- **AJAX Calls**: Mengikuti pattern response handling aplikasi

## Testing

### 1. Functional Testing
- ✅ Filter tanggal berfungsi dengan benar
- ✅ Export dengan filter berfungsi
- ✅ CRUD operations berfungsi normal
- ✅ Access control sesuai role user
- ✅ Responsive table berfungsi

### 2. Integration Testing
- ✅ Routes terdaftar dengan benar
- ✅ Controller methods dapat diakses
- ✅ View rendering tanpa error
- ✅ Database queries berjalan dengan benar

## Catatan Penting

### 1. Backward Compatibility
- Method `delete()` tetap mendukung POST data
- Export function tetap berfungsi tanpa filter
- Existing functionality tidak terpengaruh

### 2. Security
- User filtering tetap diterapkan
- Access control berdasarkan role
- Input validation tetap berjalan

### 3. Performance
- Query optimization dengan proper indexing
- Efficient JOIN operations
- Minimal database calls

## Kesimpulan

Implementasi custom Buku Induk Penduduk berhasil dilakukan dengan:
- ✅ Tidak bergantung pada CrudCore
- ✅ Filter tanggal yang berfungsi
- ✅ Export dengan filter
- ✅ Access control yang proper
- ✅ Pattern yang konsisten dengan referensi PeraturanDesa
- ✅ Responsive dan user-friendly interface

Semua fitur telah ditest dan berfungsi dengan baik sesuai dengan requirement yang diminta.
