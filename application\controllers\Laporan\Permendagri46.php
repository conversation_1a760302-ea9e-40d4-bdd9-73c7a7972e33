<?php
defined('BASEPATH') or die('No direct script access allowed!');

use <PERSON><PERSON><PERSON>\Html2Pdf\Html2Pdf;

/**
 * @property Indukpenduduks $indukpenduduks
 * @property Mutasipenduduks $mutasipenduduks
 * @property Rekappenduduks $rekappenduduks
 * @property Penduduksementaras $penduduksementaras
 * @property Bukukks $bukukks
 * @property Bukuktps $bukuktps
 * @property MasterDataPenduduks $masterdatapenduduk
 */
class Permendagri46 extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        // Load models
        $this->load->model('Indukpenduduks', 'indukpenduduks');
        $this->load->model('Mutasipenduduks', 'mutasipenduduks');
        $this->load->model('Rekappenduduks', 'rekappenduduks');
        $this->load->model('Penduduksementaras', 'penduduksementaras');
        $this->load->model('Bukukks', 'bukukks');
        $this->load->model('Bukuktps', 'bukuktps');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Laporan Format Permendagri 46';
        $data['content'] = 'laporan/permendagri46/index';

        return $this->load->view('master', $data);
    }

    /**
     * Cetak Buku Induk Penduduk
     */
    public function buku_induk_penduduk()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'BUKU INDUK PENDUDUK';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['penduduk'] = $this->indukpenduduks->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');

        $this->generatePDF('laporan/permendagri46/buku_induk_penduduk', $data, 'Buku_Induk_Penduduk_' . date('Y-m-d'));
    }

    /**
     * Cetak Buku Mutasi Penduduk Desa
     */
    public function buku_mutasi_penduduk()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        // Filter by date range if provided
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');

        if ($start_date) {
            $where['DATE(tanggal) >='] = $start_date;
        }
        if ($end_date) {
            $where['DATE(tanggal) <='] = $end_date;
        }

        $data = array();
        $data['title'] = 'BUKU MUTASI PENDUDUK DESA';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['mutasi'] = $this->mutasipenduduks->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');
        $data['periode'] = '';

        if ($start_date && $end_date) {
            $data['periode'] = 'Periode: ' . date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date));
        }

        $this->generatePDF('laporan/permendagri46/buku_mutasi_penduduk', $data, 'Buku_Mutasi_Penduduk_' . date('Y-m-d'));
    }

    /**
     * Cetak Rekapitulasi Jumlah Penduduk
     */
    public function rekapitulasi_penduduk()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        // Filter by period if provided
        $periode = $this->input->get('periode');
        if ($periode) {
            $where['periode'] = $periode;
        }

        $data = array();
        $data['title'] = 'REKAPITULASI JUMLAH PENDUDUK';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['rekapitulasi'] = $this->rekappenduduks->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');
        $data['periode_filter'] = $periode;

        // Get statistics from Master Data if available
        $statistics = $this->masterdatapenduduk->getStatistics(getCurrentUser()->id ?? null);
        $data['statistics'] = $statistics;

        $this->generatePDF('laporan/permendagri46/rekapitulasi_penduduk', $data, 'Rekapitulasi_Penduduk_' . date('Y-m-d'));
    }

    /**
     * Cetak Buku Penduduk Sementara
     */
    public function buku_penduduk_sementara()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'BUKU PENDUDUK SEMENTARA';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['penduduk_sementara'] = $this->penduduksementaras->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');

        $this->generatePDF('laporan/permendagri46/buku_penduduk_sementara', $data, 'Buku_Penduduk_Sementara_' . date('Y-m-d'));
    }

    /**
     * Cetak Buku KTP dan KK
     */
    public function buku_ktp_kk()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'BUKU KARTU TANDA PENDUDUK DAN KARTU KELUARGA';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['ktp'] = $this->bukuktps->result($where);
        $data['kk'] = $this->bukukks->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');

        $this->generatePDF('laporan/permendagri46/buku_ktp_kk', $data, 'Buku_KTP_KK_' . date('Y-m-d'));
    }

    /**
     * Generate PDF using Html2Pdf
     */
    private function generatePDF($view, $data, $filename)
    {
        try {
            // Load view content
            $html = $this->load->view($view, $data, true);

            // Initialize Html2Pdf
            $html2pdf = new Html2Pdf('P', 'A4', 'id', true, 'UTF-8', array(10, 10, 10, 10));

            // Set PDF properties
            $html2pdf->pdf->SetDisplayMode('fullpage');
            $html2pdf->pdf->SetTitle($data['title']);
            $html2pdf->pdf->SetAuthor('SIAPKADA - Sistem Administrasi Pemerintahan Desa');
            $html2pdf->pdf->SetCreator('SIAPKADA');
            $html2pdf->pdf->SetSubject($data['title']);

            // Write HTML content
            $html2pdf->writeHTML($html);

            // Output PDF
            $html2pdf->output($filename . '.pdf', 'D');
        } catch (Exception $e) {
            show_error('Error generating PDF: ' . $e->getMessage());
        }
    }

    /**
     * Preview laporan sebelum cetak
     */
    public function preview($jenis_laporan)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        switch ($jenis_laporan) {
            case 'buku_induk_penduduk':
                $this->preview_buku_induk_penduduk();
                break;
            case 'buku_mutasi_penduduk':
                $this->preview_buku_mutasi_penduduk();
                break;
            case 'rekapitulasi_penduduk':
                $this->preview_rekapitulasi_penduduk();
                break;
            case 'buku_penduduk_sementara':
                $this->preview_buku_penduduk_sementara();
                break;
            case 'buku_ktp_kk':
                $this->preview_buku_ktp_kk();
                break;
            default:
                show_404();
        }
    }

    private function preview_buku_induk_penduduk()
    {
        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'BUKU INDUK PENDUDUK';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['penduduk'] = $this->indukpenduduks->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');
        $data['is_preview'] = true;

        $this->load->view('laporan/permendagri46/buku_induk_penduduk', $data);
    }

    private function preview_buku_mutasi_penduduk()
    {
        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'BUKU MUTASI PENDUDUK DESA';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['mutasi'] = $this->mutasipenduduks->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');
        $data['is_preview'] = true;

        $this->load->view('laporan/permendagri46/buku_mutasi_penduduk', $data);
    }

    private function preview_rekapitulasi_penduduk()
    {
        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'REKAPITULASI JUMLAH PENDUDUK';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['rekapitulasi'] = $this->rekappenduduks->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');
        $data['is_preview'] = true;

        // Get statistics from Master Data
        $statistics = $this->masterdatapenduduk->getStatistics(getCurrentUser()->id ?? null);
        $data['statistics'] = $statistics;

        $this->load->view('laporan/permendagri46/rekapitulasi_penduduk', $data);
    }

    private function preview_buku_penduduk_sementara()
    {
        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'BUKU PENDUDUK SEMENTARA';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['penduduk_sementara'] = $this->penduduksementaras->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');
        $data['is_preview'] = true;

        $this->load->view('laporan/permendagri46/buku_penduduk_sementara', $data);
    }

    private function preview_buku_ktp_kk()
    {
        $where = [];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'BUKU KARTU TANDA PENDUDUK DAN KARTU KELUARGA';
        $data['subtitle'] = 'Sesuai Format Permendagri No. 46 Tahun 2016';
        $data['ktp'] = $this->bukuktps->result($where);
        $data['kk'] = $this->bukukks->result($where);
        $data['user'] = getCurrentUser();
        $data['tanggal_cetak'] = date('d F Y');
        $data['is_preview'] = true;

        $this->load->view('laporan/permendagri46/buku_ktp_kk', $data);
    }
}
