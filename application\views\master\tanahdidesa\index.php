<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active">Buku Tanah di Desa</li>
                </ul>
            </div>
            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Buku Tanah di Desa</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4>Data Buku Tanah di Desa</h4>
                </div>
                <div>
                    <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary">
                        <i class="fa fa-plus"></i>
                        <span>Tambah</span>
                    </a>
                </div>
            </div>

            <div class="card-body">

                <form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
                    <div class="row align-items-end mb-4">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Mulai</label>
                                <input type="date" name="startdate" class="form-control" value="<?= $startdate ?>" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Selesai</label>
                                <input type="date" name="enddate" class="form-control" value="<?= $enddate ?>" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="<?= base_url(uri_string()) ?>" class="btn btn-warning">Reset</a>
                                <button type="button" class="btn btn-danger" onclick="exportData()">Export</button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Data Table -->
                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <th>No. Urut</th>
                                <th>Nama Pemilik</th>
                                <th>Luas (m²)</th>
                                <th>Status Hak Tanah</th>
                                <th>Penggunaan Tanah</th>
                                <th>Keterangan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tanah_data as $tanah): ?>
                                <tr>
                                    <td><?= $tanah->nomor_urut ?></td>
                                    <td><?= htmlspecialchars($tanah->nama_pemilik) ?></td>
                                    <td class="text-right"><?= number_format($tanah->jumlah_m2, 0, ',', '.') ?></td>
                                    <td>
                                        <?php
                                        $badge_class = 'badge-secondary';
                                        if (in_array($tanah->status_hak_tanah, ['HM', 'HGB', 'HP', 'HGU', 'HPL'])) {
                                            $badge_class = 'badge-success';
                                        }
                                        ?>
                                        <span class="badge <?= $badge_class ?>"><?= $tanah->status_hak_tanah ?></span>
                                    </td>
                                    <td><?= $tanah->penggunaan_tanah ?></td>
                                    <td>
                                        <?php if (strlen($tanah->keterangan) > 50): ?>
                                            <?= substr($tanah->keterangan, 0, 50) ?>...
                                        <?php else: ?>
                                            <?= $tanah->keterangan ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url(uri_string() . '/edit/' . $tanah->id) ?>" class="btn btn-primary btn-sm" title="Edit">
                                            <i class="fa fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteData('<?= htmlspecialchars($tanah->nama_pemilik) ?>', <?= $tanah->id ?>)" title="Hapus">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportData() {
        let startdate = $('input[name="startdate"]').val();
        let enddate = $('input[name="enddate"]').val();

        window.open(`<?= base_url(uri_string()) ?>/export?startdate=${startdate}&enddate=${enddate}`, '_blank');
    }
</script>