<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item" aria-current="page">Buku Ekspedisi</li>
                </ul>
            </div>
            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Buku Ekspedisi</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4>Data Buku Ekspedisi</h4>
                </div>

                <div>
                    <?php if (isBPD()): ?>
                        <a href="<?= base_url('master/ekspedisi/add') ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i>
                            <span>Tambah</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <form action="<?= base_url('master/ekspedisi') ?>" method="GET" autocomplete="off">
                    <div class="row align-items-end mb-4">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Mulai</label>
                                <input type="date" name="startdate" class="form-control" value="<?= $startdate ?>" />
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Selesai</label>
                                <input type="date" name="enddate" class="form-control" value="<?= $enddate ?>" />
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">Filter</button>

                                <a href="<?= base_url('master/ekspedisi') ?>" class="btn btn-warning">Reset</a>

                                <button type="button" class="btn btn-danger" onclick="exportData()">Export</button>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <th>No. Urut</th>
                                <th>Tanggal Pengiriman</th>
                                <th>Tanggal Surat</th>
                                <th>Nomor Surat</th>
                                <th>Isi Singkat Surat</th>
                                <th>Ditujukan Kepada</th>
                                <th>Keterangan</th>
                                <th>File Surat</th>
                                <th>Dibuat Oleh</th>
                                <?php if (isBPD()): ?>
                                    <th>Action</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ekspedisi as $item): ?>
                                <tr>
                                    <td><?= $item->nomor_urut ?></td>
                                    <td><?= $item->tanggal_pengiriman ? date('d/m/Y', strtotime($item->tanggal_pengiriman)) : '-' ?></td>
                                    <td><?= $item->tanggal_surat ? date('d/m/Y', strtotime($item->tanggal_surat)) : '-' ?></td>
                                    <td><?= $item->nomor_surat ?: '-' ?></td>
                                    <td><?= $item->isi_singkat_surat ?: '-' ?></td>
                                    <td><?= $item->ditujukan_kepada ?: '-' ?></td>
                                    <td><?= $item->keterangan ?: '-' ?></td>
                                    <td>
                                        <?php if ($item->file_surat): ?>
                                            <a href="<?= base_url('uploads/ekspedisi/' . $item->file_surat) ?>" target="_blank" class="btn btn-sm btn-info">
                                                <i class="fa fa-download"></i> Download
                                            </a>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $item->createdname ?: $item->createdusername ?></td>
                                    <?php if (isBPD()): ?>
                                        <td>
                                            <a href="<?= base_url('master/ekspedisi/edit/' . $item->id) ?>" class="btn btn-sm btn-primary">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteData('<?= base_url('master/ekspedisi/delete/' . $item->id) ?>')">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportData() {
        let startdate = $('input[name="startdate"]').val();
        let enddate = $('input[name="enddate"]').val();

        window.open(`<?= base_url('master/ekspedisi') ?>/export?startdate=${startdate}&enddate=${enddate}`, '_blank');
    }

    function deleteData(url) {
        if (confirm('Apakah Anda yakin ingin menghapus data ini?')) {
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'OK') {
                        alert('Data berhasil dihapus');
                        location.reload();
                    } else {
                        alert('Gagal menghapus data: ' + response.message);
                    }
                },
                error: function() {
                    alert('Terjadi kesalahan saat menghapus data');
                }
            });
        }
    }
</script>