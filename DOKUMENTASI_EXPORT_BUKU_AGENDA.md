# Dokumentasi Export Buku Agenda

## Perubahan yang Dilakukan

### 1. Format Tabel Export PDF
File: `application/views/master/bukuagenda/export.php`

**Perubahan:**
- Mengubah format tabel dari "Peraturan di Desa" menjadi format "Buku Agenda" yang sesuai
- Menggunakan `colspan` dan `rowspan` untuk membuat sub-header tanpa tabel nested
- Struktur kolom baru:
  - **NOMOR URUT**: Nomor urut otomatis dari database (`rowspan="2"`)
  - **TANGGAL PENERIMAAN/PENGIRIMAN SURAT**: Tanggal agenda (`rowspan="2"`)
  - **SURAT MASUK**: Header utama dengan `colspan="4"` untuk 4 sub-kolom:
    - Nomor, Tanggal, Pengirim, Isi Singkat
  - **SURAT KELUAR**: Header utama dengan `colspan="4"` untuk 4 sub-kolom:
    - <PERSON>mor, Tanggal, Dituju<PERSON>, Isi <PERSON>at
  - **KETERANGAN**: Keterangan tambahan (`rowspan="2"`)

**Mapping Data:**
- **Surat Masuk**:
  - Nomor: `nomor_surat`
  - Tanggal: `tanggal`
  - Pengirim: `asal`
  - Isi Singkat: `perihal`
- **Surat Keluar**:
  - Nomor: `nomor_surat`
  - Tanggal: `tanggal`
  - Ditujukan Kepada: `tujuan`
  - Isi Surat: `perihal`

### 2. Penambahan Field nomor_urut
File: `sql/patch_agenda_add_nomor_urut.sql`

**Perubahan:**
- Menambahkan field `nomor_urut` ke tabel `agenda`
- Auto-generate nomor urut untuk data yang sudah ada

### 3. Update Model Agendas
File: `application/models/Agendas.php`

**Perubahan:**
- Menambahkan method `calculateAutoFields()` untuk auto-generate nomor urut
- Override method `insert()` dan `update()` untuk menggunakan auto-generation
- Nomor urut akan di-generate otomatis saat insert data baru

### 4. Update Controller BukuAgenda
File: `application/controllers/Master/BukuAgenda.php`

**Perubahan:**
- Mengubah urutan data dari `tanggal DESC` menjadi `nomor_urut ASC` untuk export
- Menambahkan komentar bahwa nomor_urut akan di-generate otomatis

## Cara Penggunaan

### 1. Jalankan Patch Database
```sql
-- Jalankan file ini di phpMyAdmin atau MySQL client
source sql/patch_agenda_add_nomor_urut.sql;
```

### 2. Export PDF
1. Buka halaman Buku Agenda
2. Pilih filter tanggal jika diperlukan
3. Klik tombol "Export PDF"
4. PDF akan menampilkan format tabel buku agenda yang baru

### 3. Format Output
- Data akan diurutkan berdasarkan nomor urut (ascending)
- Surat Masuk dan Surat Keluar akan ditampilkan di kolom yang berbeda
- Jika jenis agenda adalah "Surat Masuk", kolom Surat Keluar akan kosong (-)
- Jika jenis agenda adalah "Surat Keluar", kolom Surat Masuk akan kosong (-)

## Struktur Tabel Baru (Menggunakan colspan dan rowspan)

**Header Tabel:**
```html
<tr>
    <th rowspan="2">NOMOR URUT</th>
    <th rowspan="2">TANGGAL PENERIMAAN/PENGIRIMAN SURAT</th>
    <th colspan="4">SURAT MASUK</th>
    <th colspan="4">SURAT KELUAR</th>
    <th rowspan="2">KET</th>
</tr>
<tr>
    <th>NOMOR</th>
    <th>TANGGAL</th>
    <th>PENGIRIM</th>
    <th>ISI SINGKAT</th>
    <th>NOMOR</th>
    <th>TANGGAL</th>
    <th>DITUJUKAN KEPADA</th>
    <th>ISI SURAT</th>
</tr>
```

**Contoh Data:**
```
| NOMOR | TANGGAL      |    SURAT MASUK    |    SURAT KELUAR   | KET |
| URUT  | PENERIMAAN/  |-------------------|-------------------|     |
|       | PENGIRIMAN   | NO | TGL | DARI | ISI | NO | TGL | KPD | ISI |     |
|-------|--------------|----|----|------|-----|----|----|-----|-----|-----|
|   1   | 01/01/2024   |001 |01/1|Dinas |Und. | -  | -  | -   | -   | ... |
|   2   | 02/01/2024   | -  | -  | -    | -   |001 |02/1|Kep. |Srt. | ... |
```

## Testing

### 1. Test Data Baru
- Tambah agenda baru (Surat Masuk/Keluar)
- Pastikan nomor urut ter-generate otomatis
- Export PDF dan periksa format tabel

### 2. Test Data Existing
- Jalankan patch database
- Periksa nomor urut ter-generate untuk data lama
- Export PDF dan pastikan urutan benar

### 3. Test Filter
- Test export dengan filter tanggal
- Pastikan data yang di-export sesuai filter
- Periksa urutan berdasarkan nomor urut

## Catatan Penting

1. **Backup Database**: Selalu backup database sebelum menjalankan patch
2. **Nomor Urut**: Nomor urut akan di-generate otomatis dan tidak bisa diubah manual
3. **Urutan Data**: Data di-export berdasarkan nomor urut, bukan tanggal
4. **Kompatibilitas**: Perubahan ini backward compatible dengan data existing
