# Dokumentasi Sistem Administrasi Penduduk Terintegrasi

## Deskripsi Sistem

Sistem ini merupakan pengembangan dari SIAPKADA (Sistem Administrasi Pemerintahan Desa) dengan fokus pada administrasi kependudukan yang terintegrasi. Sistem ini dirancang untuk mengurangi redundansi input data dan memastikan konsistensi data di seluruh modul administrasi.

## Fitur Utama

### 1. Master Data Penduduk
- **Tujuan**: Menjadi sumber data tunggal untuk semua informasi penduduk
- **Fitur**:
  - CRUD lengkap data penduduk
  - Import/Export Excel
  - Validasi NIK otomatis
  - Statistik dan dashboard
  - Pencarian dan filter advanced

### 2. Integrasi Antar Modul
- **Auto-populate**: Data otomatis terisi dari Master Data berdasarkan NIK
- **Sinkronisasi**: Sinkronisasi data antar modul secara otomatis
- **Konsistensi**: Pengecekan dan perbaikan inkonsistensi data
- **Real-time**: Update data real-time di semua modul terkait

### 3. Laporan Permendagri 46
- **Format Standar**: Sesuai Permendagri No. 46 Tahun 2016
- **Cetak PDF**: Export laporan ke format PDF
- **Preview**: Preview sebelum cetak
- **Filter**: Filter berdasarkan periode dan kriteria tertentu

### 4. Modul Administrasi Terintegrasi
- Buku Induk Penduduk
- Buku Mutasi Penduduk
- Buku Penduduk Sementara
- Buku KTP dan KK
- Buku Tanah Desa
- Buku Inventaris Desa
- Buku Agenda Kegiatan

## Struktur Database

### Tabel Utama

#### master_data_penduduk
```sql
- id (Primary Key)
- nik (16 digit, unique)
- kk (16 digit)
- nama_lengkap
- nama_panggilan
- jenis_kelamin (L/P)
- tempat_lahir
- tanggal_lahir
- status_perkawinan
- agama
- pendidikan_terakhir
- pekerjaan
- kewarganegaraan
- alamat_jalan
- rt, rw, dusun
- kedudukan_dalam_keluarga
- golongan_darah
- nama_ayah, nama_ibu
- nomor_telepon, email
- status_hidup
- tanggal_masuk_desa
- keterangan
- userid, createdby, createddate, updatedby, updateddate
```

#### Tabel Terintegrasi
- `indukpenduduk` - Ditambahkan field untuk integrasi
- `mutasipenduduk` - Ditambahkan field NIK dan data personal
- `penduduksementara` - Ditambahkan field NIK dan data personal
- `bukukk` - Ditambahkan field alamat lengkap dan kepala keluarga
- `bukuktp` - Ditambahkan field data personal lengkap

#### Tabel Baru
- `buku_tanah_desa` - Administrasi tanah desa
- `buku_inventaris_desa` - Inventaris barang desa
- `buku_agenda_kegiatan` - Agenda kegiatan desa

## Arsitektur Sistem

### Model-View-Controller (MVC)
```
Controllers/
├── Master/
│   ├── IndukPenduduk.php
│   ├── BukuTanahDesa.php
│   └── BukuInventarisDesa.php
└── Laporan/
    └── Permendagri46.php

Models/
├── Indukpenduduks.php (updated)
├── BukuTanahDesas.php
├── BukuInventarisDesas.php
└── BukuAgendaKegiatans.php

Views/
├── master/indukpenduduk/ (updated)
├── laporan/permendagri46/
└── components/
    └── nik_search_widget.php

Libraries/
└── IntegrationService.php

Helpers/
└── form_validation_helper.php
```

### Service Layer
- **IntegrationService**: Menangani integrasi data antar modul
- **ValidationHelper**: Validasi form yang konsisten
- **NIKSearchWidget**: Komponen pencarian NIK yang reusable

## Fitur Integrasi

### 1. Auto-Populate Data
Ketika user memasukkan NIK di form manapun:
1. Sistem mencari data di Master Data Penduduk
2. Jika ditemukan, form otomatis terisi
3. Jika tidak ditemukan, user dapat menambah ke Master Data

### 2. Sinkronisasi Data
- **Individual**: Sinkronisasi berdasarkan NIK tertentu
- **Massal**: Sinkronisasi semua data dari Master Data
- **Otomatis**: Sinkronisasi saat ada perubahan data

### 3. Pengecekan Konsistensi
- Deteksi data yang tidak konsisten antar modul
- Laporan inkonsistensi dengan detail
- Perbaikan otomatis inkonsistensi

## Import/Export Excel

### Template Import
Setiap modul memiliki template Excel standar dengan:
- Header yang jelas
- Contoh data
- Validasi format
- Panduan pengisian

### Proses Import
1. Upload file Excel/CSV
2. Validasi format dan data
3. Auto-populate dari Master Data (jika NIK tersedia)
4. Insert/update data ke database
5. Laporan hasil import

### Export Data
- Export semua data atau berdasarkan filter
- Format Excel dengan formatting yang rapi
- Auto-size kolom
- Header yang informatif

## Laporan Permendagri 46

### Format Laporan
1. **Buku Induk Penduduk**
   - Format sesuai Permendagri 46
   - Data lengkap penduduk
   - Nomor urut otomatis

2. **Buku Mutasi Penduduk**
   - Catatan perpindahan penduduk
   - Filter berdasarkan periode
   - Detail alasan mutasi

3. **Rekapitulasi Penduduk**
   - Statistik kependudukan
   - Grafik distribusi
   - Persentase berdasarkan kategori

4. **Buku Penduduk Sementara**
   - Data penduduk non-permanen
   - Periode tinggal
   - Status kependudukan

5. **Buku KTP dan KK**
   - Catatan penerbitan dokumen
   - Status dokumen
   - Masa berlaku

### Fitur Cetak
- Preview sebelum cetak
- Export ke PDF
- Header dan footer otomatis
- Watermark dan metadata

## User Experience

### 1. Dashboard Informatif
- Statistik real-time
- Quick actions
- Recent activities
- Integration status

### 2. Pencarian Cepat
- Modal pencarian NIK
- Auto-complete
- Hasil pencarian detail
- Link ke edit/view data

### 3. Form User-Friendly
- Validasi real-time
- Auto-populate dari Master Data
- Error messages yang jelas
- Progress indicator untuk proses panjang

### 4. Responsive Design
- Mobile-friendly
- Touch-optimized
- Adaptive layout
- Fast loading

## Keamanan dan Validasi

### 1. Validasi Data
- NIK: Format 16 digit, validasi tanggal lahir
- Email: Format email yang valid
- Nomor telepon: Format Indonesia
- Tanggal: Format dan range yang valid

### 2. Authorization
- Role-based access control
- User-specific data filtering
- Audit trail untuk semua perubahan

### 3. Data Integrity
- Foreign key constraints
- Unique constraints untuk NIK
- Soft delete untuk data penting
- Backup otomatis

## Instalasi dan Konfigurasi

### 1. Requirements
- PHP 7.4+
- MySQL 5.7+
- CodeIgniter 3.x
- PhpSpreadsheet untuk Excel
- Html2Pdf untuk laporan PDF

### 2. Database Setup
```sql
-- Jalankan script SQL patch
source sql/patch_update_administrasi_penduduk.sql;
```

### 3. Configuration
```php
// application/config/config.php
$config['base_url'] = 'http://your-domain.com/';

// application/config/database.php
$db['default']['hostname'] = 'localhost';
$db['default']['username'] = 'your_username';
$db['default']['password'] = 'your_password';
$db['default']['database'] = 'your_database';
```

### 4. File Permissions
```bash
chmod 755 uploads/
chmod 755 uploads/temp/
```

## Maintenance dan Monitoring

### 1. Log System
- Error logs untuk debugging
- Activity logs untuk audit
- Performance logs untuk optimasi

### 2. Backup Strategy
- Daily database backup
- Weekly full system backup
- Monthly archive backup

### 3. Performance Monitoring
- Database query optimization
- Memory usage monitoring
- Response time tracking

## Troubleshooting

### Common Issues

1. **Import Excel Gagal**
   - Periksa format file (xlsx, xls, csv)
   - Pastikan ukuran file < 10MB
   - Validasi format data sesuai template

2. **Sinkronisasi Error**
   - Periksa koneksi database
   - Validasi data Master Data Penduduk
   - Check user permissions

3. **Laporan PDF Error**
   - Pastikan library Html2Pdf terinstall
   - Check memory limit PHP
   - Validasi data yang akan dicetak

### Support
Untuk bantuan teknis, hubungi tim development atau buat issue di repository sistem.
