<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>dudu<PERSON></title>

    <style>
        /* Area cetak & margin kertas */
        @page {
            size: A4 landscape;
            margin: 12mm;
        }

        html,
        body {
            margin: 0;
            padding: 0;
        }

        * {
            box-sizing: border-box;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }

        /* Lebar area cetak A4 setelah margin: 210 - (12+12) = 186mm */
        .pdf-container {
            width: 273mm;
            margin: 0 auto;
            margin-top: 12mm;
        }

        img {
            max-width: 100%;
            height: auto;
            display: inline-block;
        }

        table.table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        table.table th,
        table.table td {
            border: 0.5px solid #000;
            text-align: left;
            padding: 8px;
            word-wrap: break-word;
            overflow-wrap: anywhere;
            font-size: 10px;
        }

        table.table th {
            text-align: center;
            font-weight: normal;
        }

        /* Header di tengah tanpa mendorong layout */
        table.center {
            margin: 0 auto;
            width: 100%;
            border-collapse: collapse;
        }

        table.center th,
        table.center td {
            border: 0;
            padding: 0;
        }

        hr {
            border: 0;
            border-top: 1px solid #000;
            margin: 8px 0 16px;
        }

        /* Tabel tanda tangan: 3 kolom proporsional yang pas area cetak */
        .sig {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .sig td {
            border: 0;
            padding: 4px 0;
            text-align: center;
        }

        .sig .spacer {
            width: 18.6mm;
        }

        .sig .wide {
            width: calc((100% - 18.6mm)/2);
        }

        /* Page break utility */
        .page-break {
            page-break-before: always;
        }

        .sig {
            page-break-inside: avoid;
        }
    </style>
</head>

<body>
    <div class="pdf-container">

        <table class="center" style="text-align:center; margin-bottom: 20px;">
            <tr>
                <th rowspan="4" style="width: 30mm; text-align:right; padding-right:10mm;">
                    <img src="<?= base_url('/assets/img/logo-bpd.png') ?>" alt="logo" style="width: 26mm; height: 26mm;">
                </th>
                <th><b>BADAN PERMUSYAWARATAN DESA</b></th>
            </tr>
            <tr>
                <th><b>(BPD)</b></th>
            </tr>
            <tr>
                <th><b>DESA <?= $user->desaname ?> KECAMATAN <?= $user->kecamatanname ?></b></th>
            </tr>
            <tr>
                <th><b>KABUPATEN <?= $user->kabkotaname ?></b></th>
            </tr>
        </table>

        <b>
            <hr>
        </b>
        <div style="text-align: center; margin-bottom: 30px;">
            <p>
                BUKU MUTASI PENDUDUK DESA
                <?php if ($bulan && $tahun): ?>
                    BULAN <?= strtoupper($bulan_nama) ?> TAHUN <?= $tahun ?>
                <?php elseif ($bulan): ?>
                    BULAN <?= strtoupper($bulan_nama) ?>
                <?php elseif ($tahun): ?>
                    TAHUN <?= $tahun ?>
                <?php else: ?>
                    SEMUA DATA
                <?php endif; ?>
                <br>
                DESA <?= $user->desaname ?>
            </p>
        </div>


        <table class="table">
            <!-- Set lebar kolom pakai mm (total = 273mm untuk landscape) -->
            <colgroup>
                <col style="width: 15mm;"> <!-- NOMOR URUT -->
                <col style="width: 30mm;"> <!-- NAMA LENGKAP -->
                <col style="width: 20mm;"> <!-- TEMPAT LAHIR -->
                <col style="width: 15mm;"> <!-- TANGGAL LAHIR -->
                <col style="width: 12mm;"> <!-- JENIS KELAMIN -->
                <col style="width: 18mm;"> <!-- KEWARGANEGARAAN -->
                <col style="width: 20mm;"> <!-- DATANG DARI -->
                <col style="width: 15mm;"> <!-- TANGGAL DATANG -->
                <col style="width: 20mm;"> <!-- PINDAH KE -->
                <col style="width: 15mm;"> <!-- TANGGAL PINDAH -->
                <col style="width: 15mm;"> <!-- MENINGGAL -->
                <col style="width: 15mm;"> <!-- TANGGAL MENINGGAL -->
                <col style="width: 18mm;"> <!-- KETERANGAN -->
            </colgroup>

            <tr>
                <th rowspan="2" style="text-align: center;">NOMOR URUT</th>
                <th rowspan="2" style="text-align: center;">NAMA LENGKAP/ PANGGILAN</th>
                <th colspan="2" style="text-align: center;">TEMPAT & TANGGAL LAHIR</th>
                <th rowspan="2" style="text-align: center;">JENIS KELAMIN</th>
                <th rowspan="2" style="text-align: center;">KEWARGA NEGARAAN</th>
                <th colspan="2" style="text-align: center;">PENAMBAHAN</th>
                <th colspan="4" style="text-align: center;">PENGURANGAN</th>
                <th rowspan="2" style="text-align: center;">KET</th>
            </tr>
            <tr>
                <th style="text-align: center;">TEMPAT</th>
                <th style="text-align: center;">TANGGAL</th>
                <th style="text-align: center;">DATANG DARI</th>
                <th style="text-align: center;">TANGGAL</th>
                <th style="text-align: center;">PINDAH KE</th>
                <th style="text-align: center;">TANGGAL</th>
                <th style="text-align: center;">MENINGGAL</th>
                <th style="text-align: center;">TANGGAL</th>
            </tr>

            <?php foreach ($mutasi as $key => $value): ?>
                <tr>
                    <td style="text-align: center;"><?= $value->nomor_urut ?? ($key + 1) ?></td>
                    <td><?= $value->nama_lengkap ?? '-' ?></td>
                    <td><?= $value->tempat_lahir ?? '-' ?></td>
                    <td style="text-align: center;">
                        <?php if ($value->tanggal_lahir): ?>
                            <?= date('d/m/Y', strtotime($value->tanggal_lahir)) ?>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td style="text-align: center;">
                        <?php if ($value->jenis_kelamin == 'L' || $value->jenis_kelamin == 'Laki-laki'): ?>
                            L
                        <?php elseif ($value->jenis_kelamin == 'P' || $value->jenis_kelamin == 'Perempuan'): ?>
                            P
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td style="text-align: center;"><?= $value->kewarganegaraan ?? '-' ?></td>
                    <td><?= $value->datang_dari ?? '-' ?></td>
                    <td style="text-align: center;">
                        <?php if ($value->tanggal_datang): ?>
                            <?= date('d/m/Y', strtotime($value->tanggal_datang)) ?>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td><?= $value->pindah_ke ?? '-' ?></td>
                    <td style="text-align: center;">
                        <?php if ($value->tanggal_pindah): ?>
                            <?= date('d/m/Y', strtotime($value->tanggal_pindah)) ?>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td style="text-align: center;"><?= $value->meninggal ?? '-' ?></td>
                    <td style="text-align: center;">
                        <?php if ($value->tanggal_meninggal): ?>
                            <?= date('d/m/Y', strtotime($value->tanggal_meninggal)) ?>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td><?= $value->keterangan ?? '-' ?></td>
                </tr>
            <?php endforeach; ?>
        </table>

        <div class="page-break" style="margin-top: 28px;">
            <table class="sig">
                <tr>
                    <td class="wide">Mengetahui,</td>
                    <td class="spacer"></td>
                    <td class="wide"><?= $user->desaname ?>, <?= tgl_indo(getCurrentDate('Y-m-d')) ?></td>
                </tr>
                <tr>
                    <td class="wide">Kepala Desa</td>
                    <td class="spacer"></td>
                    <td class="wide">Sekretaris Desa</td>
                </tr>
            </table>

            <div style="height: 40mm;"></div>

            <table class="sig">
                <tr>
                    <td class="wide">(_______________)</td>
                    <td class="spacer"></td>
                    <td class="wide">(_______________)</td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html>