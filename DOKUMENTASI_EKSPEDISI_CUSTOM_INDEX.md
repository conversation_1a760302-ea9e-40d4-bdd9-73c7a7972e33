# Dokumentasi Buku Ekspedisi - Custom Index

## Perubahan yang Dilakukan

### 1. **Custom Index Page** ✅
File: `application/views/master/ekspedisi/index.php`

**Fitur yang ditambahkan:**
- Filter tanggal (Tanggal Mu<PERSON> dan <PERSON>)
- Tombol Filter, Reset, dan Export
- Tabel data ekspedisi dengan kolom lengkap
- Action buttons (Edit, Delete) untuk BPD
- Download file surat jika tersedia

### 2. **Controller Method Index** ✅
File: `application/controllers/Master/Ekspedisi.php`

**Method yang ditambahkan:**
- `index()` - Halaman utama dengan filter tanggal
- `add()` - Form tambah ekspedisi
- `process_add()` - Proses tambah data
- `edit($id)` - Form edit ekspedisi
- `process_edit($id)` - Proses edit data
- `delete($id)` - Hapus ekspedisi

**Perubahan pada method export:**
- Menambahkan support filter tanggal dari parameter GET
- Konsisten dengan filter di halaman index

### 3. **Form Add & Edit** ✅
Files: 
- `application/views/master/ekspedisi/add.php`
- `application/views/master/ekspedisi/edit.php`

**Field yang tersedia:**
- Tanggal Pengiriman (required)
- Tanggal Surat (optional)
- Nomor Surat (required)
- Ditujukan Kepada (required)
- Isi Singkat Surat (required)
- Keterangan (optional)

### 4. **Auto-Generation Nomor Urut** ✅
File: `application/models/Ekspedisis.php`

**Method yang ditambahkan:**
- `calculateAutoFields()` - Auto-generate nomor urut
- Override `insert()` dan `update()` untuk menggunakan auto-generation

### 5. **Routing** ✅
File: `application/config/routes.php`

**Route yang ditambahkan:**
```php
$route['master/ekspedisi'] = 'Master/Ekspedisi/index';
$route['master/ekspedisi/add'] = 'Master/Ekspedisi/add';
$route['master/ekspedisi/add/process'] = 'Master/Ekspedisi/process_add';
$route['master/ekspedisi/edit/(:num)'] = 'Master/Ekspedisi/edit/$1';
$route['master/ekspedisi/edit/(:num)/process'] = 'Master/Ekspedisi/process_edit/$1';
$route['master/ekspedisi/delete/(:num)'] = 'Master/Ekspedisi/delete/$1';
```

## Struktur Halaman Index

### Filter Section
```html
<form action="/master/ekspedisi" method="GET">
    <input type="date" name="startdate" />
    <input type="date" name="enddate" />
    <button type="submit">Filter</button>
    <a href="/master/ekspedisi">Reset</a>
    <button onclick="exportData()">Export</button>
</form>
```

### Tabel Data
**Kolom yang ditampilkan:**
1. No. Urut
2. Tanggal Pengiriman
3. Tanggal Surat
4. Nomor Surat
5. Isi Singkat Surat
6. Ditujukan Kepada
7. Keterangan
8. File Surat (dengan link download)
9. Dibuat Oleh
10. Action (Edit, Delete) - hanya untuk BPD

### JavaScript Functions
- `exportData()` - Export PDF dengan filter tanggal
- `deleteData(url)` - Konfirmasi dan hapus data via AJAX

## Cara Penggunaan

### 1. **Akses Halaman Index**
- URL: `/master/ekspedisi`
- Menampilkan semua data ekspedisi
- Support filter tanggal

### 2. **Filter Data**
- Pilih tanggal mulai dan/atau tanggal selesai
- Klik tombol "Filter"
- Data akan difilter berdasarkan tanggal pengiriman

### 3. **Reset Filter**
- Klik tombol "Reset" untuk menghapus filter
- Kembali menampilkan semua data

### 4. **Export PDF**
- Klik tombol "Export"
- PDF akan di-generate dengan data sesuai filter yang aktif
- Jika tidak ada filter, akan export semua data

### 5. **Tambah Data**
- Klik tombol "Tambah" (hanya untuk BPD)
- Isi form dengan data yang diperlukan
- Nomor urut akan di-generate otomatis

### 6. **Edit Data**
- Klik tombol edit (ikon pensil) pada baris data
- Ubah data yang diperlukan
- Klik "Update" untuk menyimpan

### 7. **Hapus Data**
- Klik tombol hapus (ikon trash) pada baris data
- Konfirmasi penghapusan
- Data akan dihapus dari database

## Keamanan & Akses

### Permission Level
- **BPD**: Full access (view, add, edit, delete, export)
- **Villages**: View dan export saja
- **Guest**: Tidak ada akses

### Validasi Data
- Tanggal pengiriman: Required
- Nomor surat: Required
- Isi singkat surat: Required
- Ditujukan kepada: Required
- Tanggal surat: Optional
- Keterangan: Optional

## Integrasi dengan Sistem Existing

### Backward Compatibility
- Semua route existing tetap berfungsi
- Dashboard, statistics, dan API endpoints tidak berubah
- Export method diupdate untuk support filter tanggal

### Database
- Menggunakan tabel `ekspedisi` yang sudah ada
- Auto-generation nomor urut untuk data baru
- Tidak ada perubahan struktur tabel

## Testing Checklist

### Functionality Tests
- [ ] Halaman index dapat diakses
- [ ] Filter tanggal berfungsi
- [ ] Tombol reset menghapus filter
- [ ] Export PDF dengan/tanpa filter
- [ ] Tambah data ekspedisi
- [ ] Edit data ekspedisi
- [ ] Hapus data ekspedisi
- [ ] Download file surat

### Permission Tests
- [ ] BPD dapat akses semua fitur
- [ ] Villages hanya dapat view dan export
- [ ] Guest tidak dapat akses

### UI/UX Tests
- [ ] Responsive design
- [ ] Form validation
- [ ] AJAX delete confirmation
- [ ] Breadcrumb navigation

## Catatan Penting

1. **Tidak bergantung pada CrudCore**: Halaman index dibuat custom tanpa menggunakan CrudCore
2. **Konsisten dengan referensi**: Mengikuti pattern dari fitur Peraturan Desa
3. **Auto-generation**: Nomor urut di-generate otomatis saat insert data baru
4. **Filter Export**: Export PDF mendukung filter tanggal yang sama dengan halaman index
