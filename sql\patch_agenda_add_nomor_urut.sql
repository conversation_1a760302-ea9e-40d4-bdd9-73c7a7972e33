-- SQL Patch: Add nomor_urut field to agenda table (Buku Agenda)

-- Add nomor_urut field if it doesn't exist
ALTER TABLE `agenda` ADD COLUMN IF NOT EXISTS `nomor_urut` int(11) DEFAULT NULL AFTER `id`;

-- Generate nomor urut untuk data yang sudah ada, diurutkan berdasarkan tanggal dan id
SET @row_number = 0;
UPDATE `agenda` 
SET `nomor_urut` = (@row_number:=@row_number + 1) 
WHERE `nomor_urut` IS NULL 
ORDER BY `tanggal` ASC, `id` ASC;
