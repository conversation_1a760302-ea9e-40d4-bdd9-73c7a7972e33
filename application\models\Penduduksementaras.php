<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PendudukSementaras extends MY_Model
{
    protected $table = 'penduduksementara';

    /**
     * Get statistics for penduduk sementara data
     */
    public function getStatistics($userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        // Total penduduk sementara
        $this->db->select('COUNT(*) as total_penduduk');
        $this->db->where($where);
        $totals = $this->db->get($this->table)->row();

        // By jenis kelamin
        $this->db->select('jenis_kelamin, COUNT(*) as jumlah');
        $this->db->where($where);
        $this->db->group_by('jenis_kelamin');
        $this->db->order_by('jumlah', 'DESC');
        $by_gender = $this->db->get($this->table)->result();

        // By kebangsaan
        $this->db->select('kebangsaan, COUNT(*) as jumlah');
        $this->db->where($where);
        $this->db->where('kebangsaan IS NOT NULL');
        $this->db->where('kebangsaan !=', '');
        $this->db->group_by('kebangsaan');
        $this->db->order_by('jumlah', 'DESC');
        $by_kebangsaan = $this->db->get($this->table)->result();

        // Currently staying (belum pergi)
        $this->db->select('COUNT(*) as masih_tinggal');
        $this->db->where($where);
        $this->db->where('(pergi_tanggal IS NULL OR pergi_tanggal > CURDATE())');
        $currently_staying = $this->db->get($this->table)->row();

        // Already left (sudah pergi)
        $this->db->select('COUNT(*) as sudah_pergi');
        $this->db->where($where);
        $this->db->where('pergi_tanggal IS NOT NULL');
        $this->db->where('pergi_tanggal <= CURDATE()');
        $already_left = $this->db->get($this->table)->row();

        // By month (current year) - using manual query
        $where_clause = '';
        if (!empty($where)) {
            $conditions = [];
            foreach ($where as $key => $value) {
                $conditions[] = "`$key` = '$value'";
            }
            $where_clause = 'WHERE ' . implode(' AND ', $conditions) . ' AND ';
        } else {
            $where_clause = 'WHERE ';
        }

        $current_year = date('Y');
        $sql_month = "SELECT
            MONTH(datang_tanggal) as bulan,
            COUNT(*) as jumlah
        FROM `{$this->table}`
        {$where_clause}YEAR(datang_tanggal) = '$current_year' AND datang_tanggal IS NOT NULL
        GROUP BY bulan
        ORDER BY bulan ASC";

        $by_month = $this->db->query($sql_month)->result();

        // Recent arrivals (last 30 days) - using manual query
        $sql_recent = "SELECT
            DATE(datang_tanggal) as tanggal,
            COUNT(*) as jumlah
        FROM `{$this->table}`
        {$where_clause}datang_tanggal >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY tanggal
        ORDER BY tanggal DESC";

        $recent_arrivals = $this->db->query($sql_recent)->result();

        // By age group - simplified approach
        $by_age = [];

        // Get age groups manually to avoid CASE statement issues
        $age_groups = [
            'Di bawah 18 tahun' => ['min' => 0, 'max' => 17],
            '18-29 tahun' => ['min' => 18, 'max' => 29],
            '30-49 tahun' => ['min' => 30, 'max' => 49],
            '50-64 tahun' => ['min' => 50, 'max' => 64],
            '65+ tahun' => ['min' => 65, 'max' => 999]
        ];

        foreach ($age_groups as $label => $range) {
            $this->db->select('COUNT(*) as jumlah');
            $this->db->where($where);
            $this->db->where('umur >=', $range['min']);
            $this->db->where('umur <=', $range['max']);
            $this->db->where('umur IS NOT NULL');
            $result = $this->db->get($this->table)->row();

            if ($result && $result->jumlah > 0) {
                $by_age[] = (object)[
                    'kelompok_umur' => $label,
                    'jumlah' => $result->jumlah
                ];
            }
        }

        return [
            'totals' => $totals,
            'currently_staying' => $currently_staying,
            'already_left' => $already_left,
            'by_gender' => $by_gender,
            'by_kebangsaan' => $by_kebangsaan,
            'by_month' => $by_month,
            'recent_arrivals' => $recent_arrivals,
            'by_age' => $by_age
        ];
    }

    /**
     * Search penduduk sementara by nama, nomor identitas, or asal
     */
    public function search($keyword, $userid = null, $limit = 10)
    {
        $this->db->group_start();
        $this->db->like('nama_lengkap', $keyword);
        $this->db->or_like('nomor_identitas', $keyword);
        $this->db->or_like('datang_dari', $keyword);
        $this->db->or_like('kebangsaan', $keyword);
        $this->db->or_like('pekerjaan', $keyword);
        $this->db->group_end();

        if ($userid) {
            $this->db->where('userid', $userid);
        }

        $this->db->limit($limit);
        $this->db->order_by('nomor_urut', 'ASC');

        return $this->db->get($this->table)->result();
    }

    /**
     * Get next nomor urut
     */
    public function getNextNomorUrut($userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        $this->db->select('MAX(nomor_urut) as max_nomor');
        $this->db->where($where);
        $result = $this->db->get($this->table)->row();

        return ($result->max_nomor ?? 0) + 1;
    }

    /**
     * Get penduduk by status (masih tinggal / sudah pergi)
     */
    public function getByStatus($status, $userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        $this->db->where($where);

        if ($status == 'masih_tinggal') {
            $this->db->where('(pergi_tanggal IS NULL OR pergi_tanggal > CURDATE())');
        } elseif ($status == 'sudah_pergi') {
            $this->db->where('pergi_tanggal IS NOT NULL');
            $this->db->where('pergi_tanggal <= CURDATE()');
        }

        return $this->db->get($this->table)->result();
    }

    /**
     * Get penduduk by date range
     */
    public function getByDateRange($start_date, $end_date, $userid = null)
    {
        $where = [];
        if ($userid) {
            $where['userid'] = $userid;
        }

        $this->db->where($where);
        $this->db->where('datang_tanggal BETWEEN', $start_date . ' AND ' . $end_date);

        return $this->db->get($this->table)->result();
    }

    /**
     * Calculate umur from tanggal_lahir
     */
    public function calculateAge($tanggal_lahir)
    {
        if (empty($tanggal_lahir)) {
            return null;
        }

        $birthDate = new DateTime($tanggal_lahir);
        $today = new DateTime('today');
        $age = $birthDate->diff($today)->y;

        return $age;
    }

    /**
     * Export data to array for Excel
     */
    public function exportToArray($where = [])
    {
        $data = $this->result($where);
        $export_data = [];

        foreach ($data as $row) {
            $export_data[] = [
                'No. Urut' => $row->nomor_urut,
                'Nama Lengkap' => $row->nama_lengkap,
                'Jenis Kelamin' => $row->jenis_kelamin,
                'Nomor Identitas' => $row->nomor_identitas,
                'Tempat Lahir' => $row->tempat_lahir,
                'Tanggal Lahir' => $row->tanggal_lahir,
                'Umur' => $row->umur,
                'Pekerjaan' => $row->pekerjaan,
                'Kebangsaan' => $row->kebangsaan,
                'Keturunan' => $row->keturunan,
                'Datang Dari' => $row->datang_dari,
                'Maksud Tujuan' => $row->maksud_tujuan_kedatangan,
                'Nama Alamat Didatangi' => $row->nama_alamat_didatangi,
                'Datang Tanggal' => $row->datang_tanggal,
                'Pergi Tanggal' => $row->pergi_tanggal,
                'Keterangan' => $row->keterangan
            ];
        }

        return $export_data;
    }

    /**
     * Check if nomor identitas already exists
     */
    public function isNomorIdentitasExists($nomor_identitas, $exclude_id = null)
    {
        if (empty($nomor_identitas)) {
            return false;
        }

        $this->db->where('nomor_identitas', $nomor_identitas);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }

        return $this->db->count_all_results($this->table) > 0;
    }
}
