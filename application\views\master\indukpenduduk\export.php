<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Buku Ekspedisi</title>
    <style>
        /* Area cetak & margin kertas */
        @page {
            size: A4 landscape;
            margin: 12mm;
        }

        html,
        body {
            margin: 0;
            padding: 0;
        }

        * {
            box-sizing: border-box;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }

        /* Lebar area cetak A4 setelah margin: 210 - (12+12) = 186mm */
        .pdf-container {
            width: 273mm;
            margin: 0 auto;
            margin-top: 12mm;
        }

        img {
            max-width: 100%;
            height: auto;
            display: inline-block;
        }

        table.table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            /* kunci lebar kolom */
            font-family: arial, sans-serif;
        }

        table.table th,
        table.table td {
            border: 0.5px solid #000;
            text-align: left;
            padding: 8px;
            word-wrap: break-word;
            overflow-wrap: anywhere;
            /* potong kata panjang */
            font-size: 12px;
        }

        table.table th {
            text-align: center;
            font-weight: normal;
        }

        /* Header di tengah tanpa mendorong layout */
        table.center {
            margin: 0 auto;
            width: 100%;
            border-collapse: collapse;
        }

        table.center th,
        table.center td {
            border: 0;
            padding: 0;
        }

        hr {
            border: 0;
            border-top: 1px solid #000;
            margin: 8px 0 16px;
        }

        /* Tabel tanda tangan: 3 kolom proporsional yang pas area cetak */
        .sig {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .sig td {
            border: 0;
            padding: 4px 0;
            text-align: center;
        }

        .sig .spacer {
            width: 18.6mm;
        }

        /* ~10% dari 186mm */
        .sig .wide {
            width: calc((100% - 18.6mm)/2);
        }
    </style>
</head>

<body>
    <div class="pdf-container">

        <!-- HEADER -->
        <table class="center" style="text-align:center; margin-bottom: 20px;">
            <tr>
                <th rowspan="4" style="width: 30mm; text-align:right; padding-right:10mm;">
                    <img src="<?= base_url('/assets/img/logo-bpd.png') ?>" alt="logo" style="width: 26mm; height: 26mm;">
                </th>
                <th><b>BADAN PERMUSYAWARATAN DESA</b></th>
            </tr>
            <tr>
                <th><b>(BPD)</b></th>
            </tr>
            <tr>
                <th><b>DESA <?= $user->desaname ?> KECAMATAN <?= $user->kecamatanname ?></b></th>
            </tr>
            <tr>
                <th><b>KABUPATEN <?= $user->kabkotaname ?></b></th>
            </tr>
        </table>

        <hr>

        <div style="text-align:center; margin-bottom: 16px;">
            <p>
                BUKU INDUK PENDUDUK<br>
                DESA <?= $user->desaname ?>
            </p>
        </div>

        <!-- DATA TABLE -->
        <table class="table">
            <!-- Set lebar kolom pakai mm (total = 273mm untuk landscape) -->
            <colgroup>
                <col style="width: 15mm;"> <!-- NOMOR URUT -->
                <col style="width: 25mm;"> <!-- NAMA LENGKAP/ PANGGILAN -->
                <col style="width: 12mm;"> <!-- JENIS KELAMIN -->
                <col style="width: 15mm;"> <!-- STATUS PERKA WINAN -->
                <col style="width: 18mm;"> <!-- TEMPAT LAHIR -->
                <col style="width: 15mm;"> <!-- TGL -->
                <col style="width: 12mm;"> <!-- AGAMA -->
                <col style="width: 18mm;"> <!-- PENDIDIKAN TERAKHIR -->
                <col style="width: 18mm;"> <!-- PEKERJAAN -->
                <col style="width: 12mm;"> <!-- DAPAT MEM BACA HURUF -->
                <col style="width: 15mm;"> <!-- KE WARGANEGARAAN -->
                <col style="width: 25mm;"> <!-- ALAMAT LENG KAP -->
                <col style="width: 20mm;"> <!-- KEDU DUKAN DLM KELU ARGA -->
                <col style="width: 20mm;"> <!-- NIK -->
                <col style="width: 18mm;"> <!-- NOMOR KK -->
                <col style="width: 15mm;"> <!-- KET -->
            </colgroup>

            <tr>
                <th rowspan="2" style="text-align: center;">NOMOR URUT</th>
                <th rowspan="2" style="text-align: center;">NAMA LENGKAP/ PANGGILAN</th>
                <th rowspan="2" style="text-align: center;">JENIS KELAMIN</th>
                <th rowspan="2" style="text-align: center;">STATUS PERKA WINAN</th>
                <th colspan="2" style="text-align: center;">TEMPAT & TANGGAL LAHIR</th>
                <th rowspan="2" style="text-align: center;">AGAMA</th>
                <th rowspan="2" style="text-align: center;">PENDIDIKAN TERAKHIR</th>
                <th rowspan="2" style="text-align: center;">PEKERJAAN</th>
                <th rowspan="2" style="text-align: center;">DAPAT MEM BACA HURUF</th>
                <th rowspan="2" style="text-align: center;">KE WARGANEGARAAN</th>
                <th rowspan="2" style="text-align: center;">ALAMAT LENG KAP</th>
                <th rowspan="2" style="text-align: center;">KEDU DUKAN DLM KELU ARGA</th>
                <th rowspan="2" style="text-align: center;">NIK</th>
                <th rowspan="2" style="text-align: center;">NOMOR KK</th>
                <th rowspan="2" style="text-align: center;">KET</th>
            </tr>
            <tr>
                <th style="text-align: center;">TEMPAT LAHIR</th>
                <th style="text-align: center;">TGL</th>
            </tr>

            <?php foreach ($indukpenduduk as $key => $value): ?>
                <tr>
                    <td style="text-align: center;"><?= $value->nomor_urut ?? ($key + 1) ?></td>
                    <td><?= $value->nama_lengkap ?? $value->nama ?? '-' ?></td>
                    <td style="text-align: center;">
                        <?php if ($value->jenis_kelamin == 'L' || $value->jenis_kelamin == 'Laki-laki'): ?>
                            L
                        <?php elseif ($value->jenis_kelamin == 'P' || $value->jenis_kelamin == 'Perempuan'): ?>
                            P
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td style="text-align: center;"><?= $value->status_perkawinan ?? '-' ?></td>
                    <td><?= $value->tempat_lahir ?? '-' ?></td>
                    <td style="text-align: center;">
                        <?php if ($value->tanggal_lahir): ?>
                            <?= date('d/m/Y', strtotime($value->tanggal_lahir)) ?>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td style="text-align: center;"><?= $value->agama ?? '-' ?></td>
                    <td><?= $value->pendidikan_terakhir ?? $value->pendidikan ?? '-' ?></td>
                    <td><?= $value->pekerjaan ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->dapat_membaca_huruf ?? 'Ya' ?></td>
                    <td style="text-align: center;"><?= $value->kewarganegaraan ?? 'WNI' ?></td>
                    <td><?= $value->alamat_lengkap ?? $value->alamat ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->kedudukan_dalam_keluarga ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->nik ?? '-' ?></td>
                    <td style="text-align: center;"><?= $value->nomor_kk ?? $value->kk ?? '-' ?></td>
                    <td><?= $value->keterangan ?? '-' ?></td>
                </tr>
            <?php endforeach; ?>
        </table>

        <!-- TANDA TANGAN -->
        <div style="margin-top: 28px;">
            <table class="sig">
                <tr>
                    <td class="wide">Mengetahui,</td>
                    <td class="spacer"></td>
                    <td class="wide"><?= $user->desaname ?>, <?= tgl_indo(getCurrentDate('Y-m-d')) ?></td>
                </tr>
                <tr>
                    <td class="wide">Ketua BPD</td>
                    <td class="spacer"></td>
                    <td class="wide">Sekertaris BPD</td>
                </tr>
            </table>

            <div style="height: 40mm;"></div>

            <table class="sig">
                <tr>
                    <td class="wide">(_______________)</td>
                    <td class="spacer"></td>
                    <td class="wide">(_______________)</td>
                </tr>
            </table>
        </div>

    </div>
</body>

</html>