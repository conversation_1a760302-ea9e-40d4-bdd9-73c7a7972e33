<?php
defined('BASEPATH') or die('No direct script access allowed!');

function JSONResponse($data = array())
{
    $CI = &get_instance();
    $CI->output->set_content_type('application/json');

    echo json_encode($data);
}

function JSONResponseDefault($result, $message)
{
    return JSONResponse(array(
        'RESULT' => $result,
        'MESSAGE' => $message
    ));
}

function getPost($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->post($index)) {
        if (!is_array($CI->input->post($index))) {
            return trim($CI->input->post($index));
        } else {
            return $CI->input->post($index);
        }
    }

    return $default;
}

function getGet($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->get($index)) {
        return $CI->input->get($index);
    }

    return $default;
}

function getModels($models = array())
{
    $ci = &get_instance();

    foreach ($models as $key => $value) {
        $ci->load->model($key, $value);
    }
}

function getSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->userdata($index);
}

function setSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->set_userdata($index);
}

function hasSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->has_userdata($index);
}

function unsetSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->unset_userdata($index);
}

function destroySession()
{
    $CI = &get_instance();

    return $CI->session->sess_destroy();
}

function getCurrentIdUser()
{
    return getSessionValue('USERID');
}

function getCurrentUser($id = null)
{
    $CI = &get_instance();

    return $CI->db->get_where('msusers', array('id' => $id ?? getCurrentIdUser()))->row();
}

function isLogin()
{
    return getSessionValue('ISLOGIN');
}

if (!function_exists('str_contains')) {
    function str_contains($needle, $haystack)
    {
        return stripos($needle, $haystack) !== false;
    }
}

function IDR($nominal, $digit = 0, $pemisah = '.', $rupiah = ',')
{
    return number_format($nominal, $digit, $pemisah, $rupiah);
}

function getCurrentDate($format = 'Y-m-d H:i:s')
{
    date_default_timezone_set("Asia/Makassar");
    return date($format);
}

function getContents($feature, $name, $row = null, $other = array())
{
    $output = array();
    $ci = &get_instance();

    switch ($feature) {
        case 'jabatan':
            if ($name == 'index') {
                $output = array(
                    'table' => [
                        'name' => 'Nama',
                        'ordering' => 'Urutan',
                        'type' => 'Tipe Jabatan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/jabatan/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.name}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result'
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nama Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_name',
                                    'placeholder' => 'Masukkan Nama Jabatan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Urutan Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'required_ordering',
                                    'placeholder' => 'Masukkan Urutan Jabatan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tipe Jabatan',
                                    'type' => 'select',
                                    'variable' => 'required_type',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Operator Desa',
                                                'name' => 'Operator Desa'
                                            ],
                                            [
                                                'id' => 'BPD',
                                                'name' => 'BPD'
                                            ],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->type : null
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'suratkeluar':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal Surat',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Surat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomorsurat',
                                    'placeholder' => 'Masukkan Nomor Surat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Hal dan Isi Singkat',
                                    'type' => 'textarea',
                                    'variable' => 'required_isisurat',
                                    'placeholder' => 'Masukkan Hal dan Isi Singkat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tujuan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tujuan',
                                    'placeholder' => 'Masukkan Tujuan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Masukkan Keterangan',
                                ],
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'suratmasuk':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal Surat',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Surat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomorsurat',
                                    'placeholder' => 'Masukkan Nomor Surat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Instansi Pengirim',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_instansi',
                                    'placeholder' => 'Masukkan Instansi Pengirim',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Hal dan Isi Singkat',
                                    'type' => 'textarea',
                                    'variable' => 'required_isisurat',
                                    'placeholder' => 'Masukkan Hal dan Isi Singkat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Masukkan Keterangan',
                                ],
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'guestbook':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Tamu',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_jabatan',
                                    'placeholder' => 'Masukkan Jabatan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Instansi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_instansi',
                                    'placeholder' => 'Masukkan Instansi',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Alamat',
                                    'type' => 'textarea',
                                    'variable' => 'required_alamat',
                                    'placeholder' => 'Masukkan Alamat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Keperluan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_keperluan',
                                    'placeholder' => 'Masukkan Keperluan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Foto',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'foto',
                                    'required' => $name == 'add' ? true : false,
                                    'attr' => [
                                        'accept' => 'image/*'
                                    ],
                                    'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'aspirasimasyarakat':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Aspirasi Masyarakat',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser(),
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama/Lembaga Pihak Penyampai Aspirasi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama/Lembaga Pihak Penyampai Aspirasi',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Aspirasi yang Disampaikan',
                                    'type' => 'textarea',
                                    'variable' => 'required_aspirasi',
                                    'placeholder' => 'Masukkan Aspirasi yang Disampaikan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tindak Lanjut',
                                    'type' => 'textarea',
                                    'variable' => 'required_tindaklanjut',
                                    'placeholder' => 'Masukkan Tindak Lanjut',
                                    'required' => true,
                                ],
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'daftarhadir':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Daftar Hadir',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser(),
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'required_gender',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Laki - laki',
                                                'name' => 'Laki - laki'
                                            ],
                                            [
                                                'id' => 'Perempuan',
                                                'name' => 'Perempuan'
                                            ]
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->gender : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_jabatan',
                                    'placeholder' => 'Masukkan Jabatan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Instansi / Desa',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_instansi',
                                    'placeholder' => 'Masukkan Instansi / Desa',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Alamat',
                                    'type' => 'textarea',
                                    'variable' => 'required_alamat',
                                    'placeholder' => 'Masukkan Alamat',
                                    'required' => true,
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'anggotabpd':
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Anggota BPD',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nama Lengkap',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama Lengkap',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Alamat',
                                    'type' => 'textarea',
                                    'variable' => 'required_address',
                                    'placeholder' => 'Masukkan Alamat',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'NIK',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_validated_nip',
                                    'placeholder' => 'Masukkan NIK',
                                    'required' => true,
                                    'attr' => [
                                        'minlength' => 16,
                                        'maxlength' => 16,
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'required_jenis_kelamin',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Laki - laki',
                                                'name' => 'Laki - laki'
                                            ],
                                            [
                                                'id' => 'Perempuan',
                                                'name' => 'Perempuan'
                                            ]
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tempat Lahir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tempat_lahir',
                                    'placeholder' => 'Masukkan Tempat Lahir',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Lahir',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'required_tanggal_lahir',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Agama',
                                    'type' => 'select',
                                    'variable' => 'required_agama',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Islam',
                                                'name' => 'Islam'
                                            ],
                                            [
                                                'id' => 'Kristen',
                                                'name' => 'Kristen'
                                            ],
                                            [
                                                'id' => 'Katolik',
                                                'name' => 'Katolik'
                                            ],
                                            [
                                                'id' => 'Hindu',
                                                'name' => 'Hindu'
                                            ],
                                            [
                                                'id' => 'Budha',
                                                'name' => 'Budha'
                                            ],
                                            [
                                                'id' => 'Kong Hu Cu',
                                                'name' => 'Kong Hu Cu'
                                            ],
                                            [
                                                'id' => 'Lainnya',
                                                'name' => 'Lainnya'
                                            ]
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jabatan',
                                    'type' => 'select',
                                    'variable' => 'required_positionid',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => $ci->db->order_by('ordering', 'ASC')->get_where('jabatan', array(
                                            'type' => 'BPD'
                                        ))->result(),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->positionid : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Pendidikan Terakhir',
                                    'type' => 'select',
                                    'variable' => 'required_pendidikan_terakhir',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'TK/PAUD',
                                                'name' => 'TK/PAUD'
                                            ],
                                            [
                                                'id' => 'SD/MI',
                                                'name' => 'SD/MI'
                                            ],
                                            [
                                                'id' => 'SMP/MTs',
                                                'name' => 'SMP/MTs'
                                            ],
                                            [
                                                'id' => 'SMA/SMK',
                                                'name' => 'SMA/SMK'
                                            ],
                                            [
                                                'id' => 'S1',
                                                'name' => 'S1'
                                            ],
                                            [
                                                'id' => 'S2',
                                                'name' => 'S2'
                                            ],
                                            [
                                                'id' => 'S3',
                                                'name' => 'S3'
                                            ],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Pengangkatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_pengangkatan',
                                    'placeholder' => 'Masukkan Nomor Pengangkatan',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Pengangkatan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_pengangkatan',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Pemberhentian',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_pemberhentian',
                                    'placeholder' => 'Masukkan Nomor Pemberhentian',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Pemberhentian',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_pemberhentian',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Status',
                                    'type' => 'select',
                                    'variable' => 'keterangan',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            [
                                                'id' => 'Aktif',
                                                'name' => 'Aktif'
                                            ],
                                            [
                                                'id' => 'Sedang Proses PAW',
                                                'name' => 'Sedang Proses PAW'
                                            ],
                                            [
                                                'id' => 'Telah Berhenti',
                                                'name' => 'Telah Berhenti'
                                            ]
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->keterangan : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Foto',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'foto',
                                    'required' => $name == 'add' ? true : false,
                                    'attr' => [
                                        'accept' => 'image/*'
                                    ],
                                    'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'keputusanbpd':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Keputusan BPD',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Tanggal Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor',
                                    'placeholder' => 'Masukkan Nomor Keputusan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tentang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tentang',
                                    'placeholder' => 'Masukkan Tentang',
                                    'required' => true
                                ],
                            ],
                        ],
                        [
                            'label' => 'Dokumen',
                            'type' => 'input',
                            'input_type' => 'file',
                            'variable' => 'document',
                            'required' => $name == 'add' ? true : false,
                            'attr' => [
                                'accept' => '.pdf,.docx,.doc,.xls,.xlsx'
                            ],
                            'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'peraturandesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Peraturan Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Peraturan Desa',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor',
                                    'placeholder' => 'Masukkan Nomor Peraturan Desa',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Peraturan Desa',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->createddate)),
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tentang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tentang',
                                    'placeholder' => 'Masukkan Tentang',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Masukkan Keterangan',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Uraian Singkat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_uraiansingkat',
                                    'placeholder' => 'Masukkan Uraian Singkat',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Kesepakatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomorkesepakatan',
                                    'placeholder' => 'Masukkan Nomor Kesepakatan',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Kesepakatan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggalkesepakatan',
                                ],
                            ]
                        ],
                        [
                            'label' => 'Dokumen',
                            'type' => 'input',
                            'input_type' => 'file',
                            'variable' => 'document',
                            'required' => $name == 'add' ? true : false,
                            'attr' => [
                                'accept' => '.pdf,.docx,.doc,.xls,.xlsx'
                            ],
                            'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'keputusankepaladesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Keputusan Kepala Desa',
                    'table' => [
                        'date' => 'Tanggal Keputusan',
                        'nomor' => 'Nomor Keputusan',
                        'tentang' => 'Tentang',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('uploads/${table.value.document}'),
                                    'class' => 'btn btn-light btn-sm',
                                    'icon' => 'fa fa-download',
                                    'text' => 'Unduh',
                                    'attr' => [
                                        'target' => '_blank'
                                    ]
                                ],
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/keputusankepaladesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.nomor}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.date' => 'DESC'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Keputusan Kepala Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'date',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->date)),
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nomor Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor',
                                    'placeholder' => 'Masukkan Nomor Keputusan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tentang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tentang',
                                    'placeholder' => 'Masukkan Tentang',
                                    'required' => true
                                ],
                            ]
                        ],
                        [
                            'label' => 'Dokumen',
                            'type' => 'input',
                            'input_type' => 'file',
                            'variable' => 'document',
                            'required' => $name == 'add' ? true : false,
                            'attr' => [
                                'accept' => '.pdf,.doc,.docx,.xls,.xlsx'
                            ],
                            'helpertext' =>  $name == 'edit' ? '*Kosongkan jika tidak ingin diubah' : null,
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'lembarandesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $startdate = getGet('startdate');
                $enddate = getGet('enddate');

                $where = array();
                if ($startdate) {
                    $where['DATE(a.tanggal_diundangkan) >='] = $startdate;
                }
                if ($enddate) {
                    $where['DATE(a.tanggal_diundangkan) <='] = $enddate;
                }

                $output = array(
                    'title' => 'Lembaran Desa & Berita Desa',
                    'table' => [
                        'jenis' => 'Jenis Peraturan',
                        'nomor_ditetapkan' => 'Nomor Ditetapkan',
                        'tanggal_ditetapkan' => 'Tanggal Ditetapkan',
                        'tentang' => 'Tentang',
                        'tanggal_diundangkan' => 'Tanggal Diundangkan',
                        'nomor_diundangkan' => 'Nomor Diundangkan',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/lembarandesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.tentang}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'filters' => [
                        'form_action' => base_url('master/lembarandesa'),
                        'method' => 'GET',
                        'data' => [
                            [
                                'type' => 'input',
                                'input_type' => 'date',
                                'variable' => 'startdate',
                                'label' => 'Tanggal Mulai (Diundangkan)',
                                'parent_class' => 'col-md-3',
                                'default' => $startdate
                            ],
                            [
                                'type' => 'input',
                                'input_type' => 'date',
                                'variable' => 'enddate',
                                'label' => 'Tanggal Selesai (Diundangkan)',
                                'parent_class' => 'col-md-3',
                                'default' => $enddate
                            ],
                            [
                                'data' => [
                                    ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Filter'],
                                    ['type' => 'a', 'class' => 'btn btn-warning', 'text' => 'Reset', 'href' => base_url('master/lembarandesa')],
                                    [
                                        'type' => 'a',
                                        'class' => 'btn btn-danger',
                                        'text' => 'Export',
                                        'href' => base_url('master/lembarandesa/export'),
                                        'attr' => ['data-export-url' => base_url('master/lembarandesa/export')]
                                    ]
                                ],
                                'parent_class' => 'col-md-6'
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'where' => $where,
                        'order_by' => ['a.tanggal_ditetapkan' => 'DESC']
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Lembaran Desa & Berita Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jenis Peraturan di Desa',
                                    'type' => 'select',
                                    'variable' => 'required_jenis',
                                    'required' => true,
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Peraturan Desa', 'name' => 'Peraturan Desa'],
                                            ['id' => 'Peraturan Bersama Kepala Desa', 'name' => 'Peraturan Bersama Kepala Desa'],
                                            ['id' => 'Peraturan Kepala Desa', 'name' => 'Peraturan Kepala Desa'],
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tentang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_tentang',
                                    'placeholder' => 'Masukkan Tentang',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor (Ditetapkan)',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nomor_ditetapkan',
                                    'placeholder' => 'Masukkan Nomor Ditetapkan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal (Ditetapkan)',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'required_tanggal_ditetapkan',
                                    'required' => true,
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : date('Y-m-d', strtotime($row->tanggal_ditetapkan)),
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor (Diundangkan)',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_diundangkan',
                                    'placeholder' => 'Masukkan Nomor Diundangkan',
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal (Diundangkan)',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_diundangkan',
                                ],
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Masukkan Keterangan',
                                ],
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'aparatdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Aparat Pemerintah Desa',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama' => 'Nama',
                        'niap' => 'NIAP',
                        'nip' => 'NIP',
                        'jenis_kelamin' => 'Jenis Kelamin',
                        'jabatan' => 'Jabatan',
                        'pendidikan_terakhir' => 'Pendidikan Terakhir',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/aparatdesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.nama}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.nomor_urut' => 'ASC'
                        ]
                    ],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/aparatdesa/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/aparatdesa/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Aparat Pemerintah Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Nama',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Masukkan Nama Lengkap',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'NIAP',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'niap',
                                    'placeholder' => 'NIAP'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'NIP',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nip',
                                    'placeholder' => 'NIP'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'jenis_kelamin',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Laki-laki', 'name' => 'Laki-laki'],
                                            ['id' => 'Perempuan', 'name' => 'Perempuan']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tempat Lahir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'tempat_lahir',
                                    'placeholder' => 'Tempat Lahir'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tanggal Lahir',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_lahir'
                                ],
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Agama',
                                    'type' => 'select',
                                    'variable' => 'agama',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Islam', 'name' => 'Islam'],
                                            ['id' => 'Kristen', 'name' => 'Kristen'],
                                            ['id' => 'Katholik', 'name' => 'Katholik'],
                                            ['id' => 'Hindu', 'name' => 'Hindu'],
                                            ['id' => 'Budha', 'name' => 'Budha'],
                                            ['id' => 'Konghucu', 'name' => 'Konghucu']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->agama : null
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Pangkat Golongan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pangkat_golongan',
                                    'placeholder' => 'Pangkat Golongan'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Jabatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_jabatan',
                                    'placeholder' => 'Jabatan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Pendidikan Terakhir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pendidikan_terakhir',
                                    'placeholder' => 'Pendidikan Terakhir'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'No. SK Pengangkatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'no_sk_pengangkatan',
                                    'placeholder' => 'Nomor SK Pengangkatan'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal SK Pengangkatan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_sk_pengangkatan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'No. SK Pemberhentian',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'no_sk_pemberhentian',
                                    'placeholder' => 'Nomor SK Pemberhentian'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal SK Pemberhentian',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_sk_pemberhentian'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Upload File Keputusan',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'file_keputusan',
                                    'accept' => '.pdf,.doc,.docx,.jpg,.jpeg,.png'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'ekspedisi':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Ekspedisi',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'tanggal_pengiriman' => 'Tanggal Pengiriman',
                        'nomor_surat' => 'Nomor Surat',
                        'tanggal_surat' => 'Tanggal Surat',
                        'isi_singkat_surat' => 'Isi Singkat Surat',
                        'ditujukan_kepada' => 'Ditujukan Kepada',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/ekspedisi/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.nomor_surat}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'order_by' => [
                            'a.nomor_urut' => 'ASC'
                        ]
                    ],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/ekspedisi/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/ekspedisi/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Ekspedisi',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Tanggal Pengiriman',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_pengiriman',
                                    'default' => $row == null ? getCurrentDate('Y-m-d') : $row->tanggal_pengiriman
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Tanggal Surat',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_surat'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nomor Surat',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_surat',
                                    'placeholder' => 'Nomor Surat'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Ditujukan Kepada',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'ditujukan_kepada',
                                    'placeholder' => 'Ditujukan Kepada'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Isi Singkat Surat Yang Dikirim',
                                    'type' => 'textarea',
                                    'variable' => 'isi_singkat_surat',
                                    'placeholder' => 'Isi singkat surat yang dikirim'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Upload File Surat',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'file_surat',
                                    'accept' => '.pdf,.doc,.docx,.jpg,.jpeg,.png'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'bukuinventarisdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }



            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Inventaris & Kekayaan Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut'
                                ],
                                [
                                    'parent_class' => 'col-md-9',
                                    'label' => 'Jenis Barang/Bangunan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_jenis_barang_bangunan',
                                    'required' => true
                                ]
                            ]
                        ],

                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Asal Barang/Bangunan',
                                    'type' => 'select',
                                    'variable' => 'asal_barang_bangunan',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Asal Barang --'],
                                            ['id' => 'APB Desa', 'name' => 'APB Desa'],
                                            ['id' => 'Bantuan Pemerintah', 'name' => 'Bantuan Pemerintah'],
                                            ['id' => 'Bantuan Provinsi', 'name' => 'Bantuan Provinsi'],
                                            ['id' => 'Bantuan Kab/Kota', 'name' => 'Bantuan Kab/Kota'],
                                            ['id' => 'Sumbangan', 'name' => 'Sumbangan']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->asal_barang_bangunan : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keadaan Awal Tahun',
                                    'type' => 'select',
                                    'variable' => 'keadaan_awal_tahun',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Baik', 'name' => 'Baik'],
                                            ['id' => 'Rusak', 'name' => 'Rusak']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->keadaan_awal_tahun : 'Baik'
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Penghapusan Barang',
                                    'type' => 'select',
                                    'variable' => 'penghapusan_barang',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Tidak Ada Penghapusan --'],
                                            ['id' => 'Rusak', 'name' => 'Rusak'],
                                            ['id' => 'Dijual', 'name' => 'Dijual'],
                                            ['id' => 'Disumbangkan', 'name' => 'Disumbangkan']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->penghapusan_barang : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Penghapusan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_penghapusan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keadaan Akhir Tahun',
                                    'type' => 'select',
                                    'variable' => 'keadaan_akhir_tahun',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Baik', 'name' => 'Baik'],
                                            ['id' => 'Rusak', 'name' => 'Rusak']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->keadaan_akhir_tahun : 'Baik'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/bukuinventarisdesa'),
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali'
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan',
                            'onclick' => 'submitForm()'
                        ]
                    ]
                );
            }

            $output['model'] = 'BukuInventarisDesas';
            break;




        case 'tanahkasdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'index') {
                $startdate = getGet('startdate');
                $enddate = getGet('enddate');

                $where = array();
                if ($startdate) {
                    $where['DATE(a.tanggal_perolehan) >='] = $startdate;
                }
                if ($enddate) {
                    $where['DATE(a.tanggal_perolehan) <='] = $enddate;
                }

                $output = array(
                    'title' => 'Buku Tanah Kas Desa',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'asal_tanah_kas_desa' => 'Asal Tanah Kas Desa',
                        'nomor_sertifikat' => 'No. Sertifikat',
                        'luas_m2' => 'Luas (m2)',
                        'jenis_tkd' => 'Jenis TKD',
                        'perolehan_tkd' => 'Perolehan TKD',
                        'lokasi' => 'Lokasi',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/tanahkasdesa/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'fa fa-edit',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.asal_tanah_kas_desa}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'fa fa-trash',
                                ]
                            ]
                        ]
                    ],
                    'filters' => [
                        'form_action' => base_url('master/tanahkasdesa'),
                        'method' => 'GET',
                        'data' => [
                            [
                                'type' => 'input',
                                'input_type' => 'date',
                                'variable' => 'startdate',
                                'label' => 'Tanggal Mulai (Perolehan)',
                                'parent_class' => 'col-md-3',
                                'default' => $startdate
                            ],
                            [
                                'type' => 'input',
                                'input_type' => 'date',
                                'variable' => 'enddate',
                                'label' => 'Tanggal Selesai (Perolehan)',
                                'parent_class' => 'col-md-3',
                                'default' => $enddate
                            ],
                            [
                                'data' => [
                                    ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Filter'],
                                    ['type' => 'a', 'class' => 'btn btn-warning', 'text' => 'Reset', 'href' => base_url('master/tanahkasdesa')],
                                    [
                                        'type' => 'a',
                                        'class' => 'btn btn-danger',
                                        'text' => 'Export',
                                        'href' => base_url('master/tanahkasdesa/export'),
                                        'attr' => ['data-export-url' => base_url('master/tanahkasdesa/export')]
                                    ]
                                ],
                                'parent_class' => 'col-md-6'
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result',
                        'where' => $where,
                        'order_by' => [
                            'a.nomor_urut' => 'ASC'
                        ]
                    ],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/tanahkasdesa/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/tanahkasdesa/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Tanah Kas Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Asal Tanah Kas Desa',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'asal_tanah_kas_desa',
                                    'placeholder' => 'Asal Tanah Kas Desa'
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Nomor Sertifikat Buku Letter C/Persil',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_sertifikat',
                                    'placeholder' => 'Nomor Sertifikat'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Luas (m2)',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'luas_m2',
                                    'placeholder' => 'Luas dalam m2',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Kelas',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'kelas',
                                    'placeholder' => 'Kelas Tanah'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Perolehan TKD',
                                    'type' => 'select',
                                    'variable' => 'perolehan_tkd',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Perolehan --'],
                                            ['id' => 'Asli Milik Desa', 'name' => 'Asli Milik Desa'],
                                            ['id' => 'Bantuan Pemerintah', 'name' => 'Bantuan Pemerintah'],
                                            ['id' => 'Bantuan Provinsi', 'name' => 'Bantuan Provinsi'],
                                            ['id' => 'Bantuan Kab/Kota', 'name' => 'Bantuan Kab/Kota'],
                                            ['id' => 'Lain-lain', 'name' => 'Lain-lain']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->perolehan_tkd : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tanggal Perolehan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_perolehan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jenis TKD',
                                    'type' => 'select',
                                    'variable' => 'jenis_tkd',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Jenis --'],
                                            ['id' => 'Sawah', 'name' => 'Sawah'],
                                            ['id' => 'Tegal', 'name' => 'Tegal'],
                                            ['id' => 'Kebun', 'name' => 'Kebun'],
                                            ['id' => 'Tambak/Kolam', 'name' => 'Tambak/Kolam'],
                                            ['id' => 'Tanah Kering/Darat', 'name' => 'Tanah Kering/Darat']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_tkd : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Patok Tanda Batas',
                                    'type' => 'select',
                                    'variable' => 'patok_tanda_batas',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Ada', 'name' => 'Ada'],
                                            ['id' => 'Tidak Ada', 'name' => 'Tidak Ada']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->patok_tanda_batas : 'Tidak Ada'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Papan Nama',
                                    'type' => 'select',
                                    'variable' => 'papan_nama',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Ada', 'name' => 'Ada'],
                                            ['id' => 'Tidak Ada', 'name' => 'Tidak Ada']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->papan_nama : 'Tidak Ada'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Lokasi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'lokasi',
                                    'placeholder' => 'Lokasi Tanah'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Peruntukan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'peruntukan',
                                    'placeholder' => 'Peruntukan Tanah'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Mutasi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'mutasi',
                                    'placeholder' => 'Mutasi'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;



        case 'tanahdidesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }

            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Tanah di Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama Per-Orangan/Badan Hukum',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama_pemilik',
                                    'placeholder' => 'Nama Pemilik',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Jumlah (m2)',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'jumlah_m2',
                                    'placeholder' => 'Jumlah dalam m2',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Status Hak Tanah',
                                    'type' => 'select',
                                    'variable' => 'status_hak_tanah',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Status Hak --'],
                                            ['id' => 'HM', 'name' => 'HM (Hak Milik)'],
                                            ['id' => 'HGB', 'name' => 'HGB (Hak Guna Bangunan)'],
                                            ['id' => 'HP', 'name' => 'HP (Hak Pakai)'],
                                            ['id' => 'HGU', 'name' => 'HGU (Hak Guna Usaha)'],
                                            ['id' => 'HPL', 'name' => 'HPL (Hak Pengelolaan)'],
                                            ['id' => 'MA', 'name' => 'MA (Milik Adat)'],
                                            ['id' => 'VI', 'name' => 'VI (Verponding Indonesia)'],
                                            ['id' => 'TN', 'name' => 'TN (Tanah Negara)']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->status_hak_tanah : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Penggunaan Tanah',
                                    'type' => 'select',
                                    'variable' => 'penggunaan_tanah',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Penggunaan --'],
                                            ['id' => 'Perumahan', 'name' => 'Perumahan'],
                                            ['id' => 'Perdagangan dan Jasa', 'name' => 'Perdagangan dan Jasa'],
                                            ['id' => 'Perkantoran', 'name' => 'Perkantoran'],
                                            ['id' => 'Industri', 'name' => 'Industri'],
                                            ['id' => 'Fasilitas Umum', 'name' => 'Fasilitas Umum'],
                                            ['id' => 'Sawah', 'name' => 'Sawah'],
                                            ['id' => 'Tegalan', 'name' => 'Tegalan'],
                                            ['id' => 'Perkebunan', 'name' => 'Perkebunan'],
                                            ['id' => 'Peternakan/Perikanan', 'name' => 'Peternakan/Perikanan'],
                                            ['id' => 'Hutan Belukar', 'name' => 'Hutan Belukar'],
                                            ['id' => 'Hutan Lebat/Lindung', 'name' => 'Hutan Lebat/Lindung'],
                                            ['id' => 'Mutasi Tanah di Desa', 'name' => 'Mutasi Tanah di Desa'],
                                            ['id' => 'Tanah Kosong', 'name' => 'Tanah Kosong'],
                                            ['id' => 'Lain-lain', 'name' => 'Lain-lain']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->penggunaan_tanah : null
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/tanahdidesa'),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }

            break;

        case 'indukpenduduk':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Induk Penduduk',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama_lengkap' => 'Nama Lengkap',
                        'nik' => 'NIK',
                        'nomor_kk' => 'No. KK',
                        'jenis_kelamin' => 'L/P',
                        'status_perkawinan' => 'Status Kawin',
                        'kedudukan_dalam_keluarga' => 'Kedudukan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/indukpenduduk/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_lengkap}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/indukpenduduk/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('cetak/buku_penduduk'),
                            'class' => 'btn btn-success',
                            'icon' => 'fa fa-print',
                            'text' => 'Cetak Permendagri 46',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/indukpenduduk/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Induk Penduduk',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama Lengkap/Panggilan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama_lengkap',
                                    'placeholder' => 'Nama Lengkap',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'jenis_kelamin',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Jenis Kelamin --'],
                                            ['id' => 'Laki-laki', 'name' => 'Laki-laki'],
                                            ['id' => 'Perempuan', 'name' => 'Perempuan']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Status Perkawinan',
                                    'type' => 'select',
                                    'variable' => 'status_perkawinan',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Status --'],
                                            ['id' => 'Belum Kawin', 'name' => 'Belum Kawin'],
                                            ['id' => 'Kawin', 'name' => 'Kawin'],
                                            ['id' => 'Cerai Hidup', 'name' => 'Cerai Hidup'],
                                            ['id' => 'Cerai Mati', 'name' => 'Cerai Mati']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->status_perkawinan : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tempat Lahir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'tempat_lahir',
                                    'placeholder' => 'Tempat Lahir'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal Lahir',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_lahir'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Agama',
                                    'type' => 'select',
                                    'variable' => 'agama',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Agama --'],
                                            ['id' => 'Islam', 'name' => 'Islam'],
                                            ['id' => 'Kristen', 'name' => 'Kristen'],
                                            ['id' => 'Katholik', 'name' => 'Katholik'],
                                            ['id' => 'Hindu', 'name' => 'Hindu'],
                                            ['id' => 'Budha', 'name' => 'Budha'],
                                            ['id' => 'Konghucu', 'name' => 'Konghucu']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->agama : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Pendidikan Terakhir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pendidikan_terakhir',
                                    'placeholder' => 'Pendidikan Terakhir'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Pekerjaan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pekerjaan',
                                    'placeholder' => 'Pekerjaan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Dapat Membaca Huruf',
                                    'type' => 'select',
                                    'variable' => 'dapat_membaca_huruf',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Ya', 'name' => 'Ya'],
                                            ['id' => 'Tidak', 'name' => 'Tidak']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->dapat_membaca_huruf : 'Ya'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Kewarganegaraan',
                                    'type' => 'select',
                                    'variable' => 'kewarganegaraan',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'WNI', 'name' => 'WNI'],
                                            ['id' => 'WNA', 'name' => 'WNA']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->kewarganegaraan : 'WNI'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'NIK',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nik',
                                    'placeholder' => '16 digit NIK'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Nomor KK',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_kk',
                                    'placeholder' => '16 digit Nomor KK'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Kedudukan Dalam Keluarga',
                                    'type' => 'select',
                                    'variable' => 'kedudukan_dalam_keluarga',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Kedudukan --'],
                                            ['id' => 'Kepala Keluarga', 'name' => 'Kepala Keluarga'],
                                            ['id' => 'Istri', 'name' => 'Istri'],
                                            ['id' => 'Anak', 'name' => 'Anak'],
                                            ['id' => 'Menantu', 'name' => 'Menantu'],
                                            ['id' => 'Cucu', 'name' => 'Cucu'],
                                            ['id' => 'Orang Tua', 'name' => 'Orang Tua'],
                                            ['id' => 'Mertua', 'name' => 'Mertua'],
                                            ['id' => 'Famili Lain', 'name' => 'Famili Lain'],
                                            ['id' => 'Pembantu', 'name' => 'Pembantu'],
                                            ['id' => 'Lainnya', 'name' => 'Lainnya']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->kedudukan_dalam_keluarga : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Alamat Lengkap',
                                    'type' => 'textarea',
                                    'variable' => 'alamat_lengkap',
                                    'placeholder' => 'Alamat lengkap tempat tinggal'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'mutasipenduduk':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Mutasi Penduduk Desa',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama_lengkap' => 'Nama Lengkap',
                        'jenis_kelamin' => 'L/P',
                        'datang_dari' => 'Datang Dari',
                        'tanggal_datang' => 'Tgl Datang',
                        'pindah_ke' => 'Pindah Ke',
                        'tanggal_pindah' => 'Tgl Pindah',
                        'meninggal' => 'Meninggal',
                        'tanggal_meninggal' => 'Tgl Meninggal',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/mutasipenduduk/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_lengkap}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/mutasipenduduk/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('cetak/buku_mutasi_penduduk'),
                            'class' => 'btn btn-success',
                            'icon' => 'fa fa-print',
                            'text' => 'Cetak Permendagri 46',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/mutasipenduduk/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Mutasi Penduduk Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama Lengkap/Panggilan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama_lengkap',
                                    'placeholder' => 'Nama Lengkap',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'jenis_kelamin',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Jenis Kelamin --'],
                                            ['id' => 'Laki-laki', 'name' => 'Laki-laki'],
                                            ['id' => 'Perempuan', 'name' => 'Perempuan']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tempat Lahir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'tempat_lahir',
                                    'placeholder' => 'Tempat Lahir'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal Lahir',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_lahir'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Kewarganegaraan',
                                    'type' => 'select',
                                    'variable' => 'kewarganegaraan',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'WNI', 'name' => 'WNI'],
                                            ['id' => 'WNA', 'name' => 'WNA']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->kewarganegaraan : 'WNI'
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => '<strong>PENAMBAHAN</strong>',
                                    'type' => 'html',
                                    'html' => '<hr style="margin: 10px 0;">'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Datang Dari',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'datang_dari',
                                    'placeholder' => 'Asal daerah datang'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Datang',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_datang'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => '<strong>PENGURANGAN</strong>',
                                    'type' => 'html',
                                    'html' => '<hr style="margin: 10px 0;">'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Pindah Ke',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pindah_ke',
                                    'placeholder' => 'Tujuan daerah pindah'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Pindah',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_pindah'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Meninggal',
                                    'type' => 'select',
                                    'variable' => 'meninggal',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Tidak', 'name' => 'Tidak'],
                                            ['id' => 'Ya', 'name' => 'Ya']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->meninggal : 'Tidak'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Tanggal Meninggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_meninggal'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;



        case 'rekappenduduk':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Rekapitulasi Jumlah Penduduk',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama_dusun' => 'Nama Dusun/Lingkungan',
                        'periode' => 'Periode',
                        'awal_jiwa' => 'Awal Jiwa',
                        'akhir_jiwa' => 'Akhir Jiwa',
                        'akhir_kk' => 'Jumlah KK',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/rekappenduduk/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_dusun}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.periode' => 'DESC', 'a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/rekappenduduk/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('cetak/buku_rekapitulasi_penduduk'),
                            'class' => 'btn btn-success',
                            'icon' => 'fa fa-print',
                            'text' => 'Cetak Permendagri 46',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/rekappenduduk/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Rekapitulasi Jumlah Penduduk',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Nama Dusun/Lingkungan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama_dusun',
                                    'placeholder' => 'Nama Dusun/Lingkungan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Periode',
                                    'type' => 'input',
                                    'input_type' => 'month',
                                    'variable' => 'required_periode',
                                    'required' => true
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => '<strong>JUMLAH PENDUDUK AWAL BULAN</strong>',
                                    'type' => 'html',
                                    'html' => '<hr style="margin: 10px 0;">'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => '<strong>a. WNA</strong>',
                                    'type' => 'html',
                                    'html' => ''
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jumlah Laki-Laki',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'awal_wna_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jumlah Perempuan',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'awal_wna_p',
                                    'default' => 0
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => '<strong>b. WNI</strong>',
                                    'type' => 'html',
                                    'html' => ''
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jumlah Laki-Laki',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'awal_wni_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jumlah Perempuan',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'awal_wni_p',
                                    'default' => 0
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'c. Jumlah Kepala Keluarga',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'awal_kk',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'd. Jumlah Anggota Keluarga',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'awal_anggota_keluarga',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'e. Jumlah Jiwa',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'awal_jiwa',
                                    'default' => 0
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => '<strong>TAMBAHAN BULAN INI</strong>',
                                    'type' => 'html',
                                    'html' => '<hr style="margin: 10px 0;">'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Lahir WNA L',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tambah_lahir_wna_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Lahir WNA P',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tambah_lahir_wna_p',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Lahir WNI L',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tambah_lahir_wni_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Lahir WNI P',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tambah_lahir_wni_p',
                                    'default' => 0
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Datang WNA L',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tambah_datang_wna_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Datang WNA P',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tambah_datang_wna_p',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Datang WNI L',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tambah_datang_wni_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Datang WNI P',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tambah_datang_wni_p',
                                    'default' => 0
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => '<strong>PENGURANGAN BULAN INI</strong>',
                                    'type' => 'html',
                                    'html' => '<hr style="margin: 10px 0;">'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Meninggal WNA L',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'kurang_meninggal_wna_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Meninggal WNA P',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'kurang_meninggal_wna_p',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Meninggal WNI L',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'kurang_meninggal_wni_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Meninggal WNI P',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'kurang_meninggal_wni_p',
                                    'default' => 0
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pindah WNA L',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'kurang_pindah_wna_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pindah WNA P',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'kurang_pindah_wna_p',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pindah WNI L',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'kurang_pindah_wni_l',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pindah WNI P',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'kurang_pindah_wni_p',
                                    'default' => 0
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => '<strong>JUMLAH PENDUDUK AKHIR BULAN (Auto Calculate)</strong>',
                                    'type' => 'html',
                                    'html' => '<hr style="margin: 10px 0;">'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Akhir KK',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'akhir_kk',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Akhir Anggota Keluarga',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'akhir_anggota_keluarga',
                                    'default' => 0
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'penduduksementara':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Penduduk Sementara',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama_lengkap' => 'Nama Lengkap',
                        'jenis_kelamin' => 'L/P',
                        'nomor_identitas' => 'No. Identitas',
                        'kebangsaan' => 'Kebangsaan',
                        'datang_dari' => 'Datang Dari',
                        'datang_tanggal' => 'Tgl Datang',
                        'pergi_tanggal' => 'Tgl Pergi',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/penduduksementara/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_lengkap}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/penduduksementara/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/penduduksementara/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Penduduk Sementara',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama Lengkap',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama_lengkap',
                                    'placeholder' => 'Nama Lengkap',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'jenis_kelamin',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Jenis Kelamin --'],
                                            ['id' => 'Laki-laki', 'name' => 'Laki-laki'],
                                            ['id' => 'Perempuan', 'name' => 'Perempuan']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nomor Identitas/Tanda Pengenal',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nomor_identitas',
                                    'placeholder' => 'KTP/Paspor/SIM/dll'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tempat Lahir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'tempat_lahir',
                                    'placeholder' => 'Tempat Lahir'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal Lahir / Umur',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_lahir'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Pekerjaan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pekerjaan',
                                    'placeholder' => 'Pekerjaan'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Kewarganegaraan - Kebangsaan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'kebangsaan',
                                    'placeholder' => 'Indonesia/Asing'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Kewarganegaraan - Keturunan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'keturunan',
                                    'placeholder' => 'Keturunan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Datang Dari',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'datang_dari',
                                    'placeholder' => 'Asal daerah datang'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Datang Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'datang_tanggal'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Maksud dan Tujuan Kedatangan',
                                    'type' => 'textarea',
                                    'variable' => 'maksud_tujuan_kedatangan',
                                    'placeholder' => 'Maksud dan tujuan kedatangan'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Nama dan Alamat Yang Didatangi',
                                    'type' => 'textarea',
                                    'variable' => 'nama_alamat_didatangi',
                                    'placeholder' => 'Nama dan alamat yang didatangi'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Pergi Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'pergi_tanggal'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'ktpkk':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'no_kk' => 'No. KK',
                        'nama_lengkap' => 'Nama Lengkap',
                        'nik' => 'NIK',
                        'jenis_kelamin' => 'L/P',
                        'agama' => 'Agama',
                        'status_hubungan_keluarga' => 'Status Keluarga',
                        'tanggal_dikeluarkan' => 'Tgl Dikeluarkan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/ktpkk/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_lengkap}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/ktpkk/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/ktpkk/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kartu Tanda Penduduk dan Buku Kartu Keluarga',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'No. KK',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'no_kk',
                                    'placeholder' => 'Nomor Kartu Keluarga'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nama Lengkap',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama_lengkap',
                                    'placeholder' => 'Nama Lengkap',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'NIK',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nik',
                                    'placeholder' => 'Nomor Induk Kependudukan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'jenis_kelamin',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Jenis Kelamin --'],
                                            ['id' => 'Laki-laki', 'name' => 'Laki-laki'],
                                            ['id' => 'Perempuan', 'name' => 'Perempuan']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_kelamin : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tempat Lahir',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'tempat_lahir',
                                    'placeholder' => 'Tempat Lahir'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tanggal Lahir',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_lahir'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Gol. Darah',
                                    'type' => 'select',
                                    'variable' => 'gol_darah',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '-', 'name' => 'Tidak Diketahui'],
                                            ['id' => 'A', 'name' => 'A'],
                                            ['id' => 'B', 'name' => 'B'],
                                            ['id' => 'AB', 'name' => 'AB'],
                                            ['id' => 'O', 'name' => 'O']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->gol_darah : '-'
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Agama',
                                    'type' => 'select',
                                    'variable' => 'agama',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Agama --'],
                                            ['id' => 'Islam', 'name' => 'Islam'],
                                            ['id' => 'Kristen', 'name' => 'Kristen'],
                                            ['id' => 'Katholik', 'name' => 'Katholik'],
                                            ['id' => 'Hindu', 'name' => 'Hindu'],
                                            ['id' => 'Budha', 'name' => 'Budha'],
                                            ['id' => 'Konghucu', 'name' => 'Konghucu']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->agama : null
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pendidikan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pendidikan',
                                    'placeholder' => 'Pendidikan Terakhir'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pekerjaan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pekerjaan',
                                    'placeholder' => 'Pekerjaan'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Status Perkawinan',
                                    'type' => 'select',
                                    'variable' => 'status_perkawinan',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Status --'],
                                            ['id' => 'Belum Kawin', 'name' => 'Belum Kawin'],
                                            ['id' => 'Kawin', 'name' => 'Kawin'],
                                            ['id' => 'Cerai Hidup', 'name' => 'Cerai Hidup'],
                                            ['id' => 'Cerai Mati', 'name' => 'Cerai Mati']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->status_perkawinan : null
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Alamat',
                                    'type' => 'textarea',
                                    'variable' => 'alamat',
                                    'placeholder' => 'Alamat lengkap'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tempat Dikeluarkan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'tempat_dikeluarkan',
                                    'placeholder' => 'Tempat dikeluarkan KTP/KK'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal Dikeluarkan',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_dikeluarkan'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Status Hubungan Keluarga',
                                    'type' => 'select',
                                    'variable' => 'status_hubungan_keluarga',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => '', 'name' => '-- Pilih Status --'],
                                            ['id' => 'Kepala Keluarga', 'name' => 'Kepala Keluarga'],
                                            ['id' => 'Istri', 'name' => 'Istri'],
                                            ['id' => 'Anak', 'name' => 'Anak'],
                                            ['id' => 'Menantu', 'name' => 'Menantu'],
                                            ['id' => 'Cucu', 'name' => 'Cucu'],
                                            ['id' => 'Orang Tua', 'name' => 'Orang Tua'],
                                            ['id' => 'Mertua', 'name' => 'Mertua'],
                                            ['id' => 'Famili Lain', 'name' => 'Famili Lain'],
                                            ['id' => 'Pembantu', 'name' => 'Pembantu'],
                                            ['id' => 'Lainnya', 'name' => 'Lainnya']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->status_hubungan_keluarga : null
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Kewarganegaraan',
                                    'type' => 'select',
                                    'variable' => 'kewarganegaraan',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'WNI', 'name' => 'WNI'],
                                            ['id' => 'WNA', 'name' => 'WNA']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->kewarganegaraan : 'WNI'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Nama Ayah',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nama_ayah',
                                    'placeholder' => 'Nama Ayah'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Nama Ibu',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'nama_ibu',
                                    'placeholder' => 'Nama Ibu'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tanggal Masuk Desa',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'tanggal_masuk_desa'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;



        case 'rkpdes':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Rencana Kerja Pembangunan Desa',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama_proyek_kegiatan' => 'Nama Proyek/Kegiatan',
                        'lokasi' => 'Lokasi',
                        'jumlah' => 'Jumlah (Rp)',
                        'pelaksana' => 'Pelaksana',
                        'tahun_anggaran' => 'Tahun',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/rkpdes/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_proyek_kegiatan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/rkpdes/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/rkpdes/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Rencana Kerja Pembangunan Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tahun Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tahun_anggaran',
                                    'placeholder' => date('Y'),
                                    'default' => date('Y')
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Lokasi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'lokasi',
                                    'placeholder' => 'Lokasi Kegiatan'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pelaksana',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pelaksana',
                                    'placeholder' => 'Pelaksana'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Nama Proyek/Kegiatan',
                                    'type' => 'textarea',
                                    'variable' => 'required_nama_proyek_kegiatan',
                                    'placeholder' => 'Nama Proyek/Kegiatan Lengkap',
                                    'required' => true
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Biaya Pemerintah',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_biaya_pemerintah',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Biaya Provinsi',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_biaya_provinsi',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Biaya Kab/Kota',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_biaya_kab_kota',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Biaya Swadaya',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_biaya_swadaya',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jumlah Total',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'jumlah',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Manfaat',
                                    'type' => 'textarea',
                                    'variable' => 'manfaat',
                                    'placeholder' => 'Manfaat dari proyek/kegiatan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'kegiatanpembangunan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kegiatan Pembangunan',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama_proyek_kegiatan' => 'Nama Proyek/Kegiatan',
                        'volume' => 'Volume',
                        'jumlah' => 'Jumlah (Rp)',
                        'sifat_proyek' => 'Sifat Proyek',
                        'pelaksana' => 'Pelaksana',
                        'tahun_anggaran' => 'Tahun',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kegiatanpembangunan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_proyek_kegiatan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/kegiatanpembangunan/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/kegiatanpembangunan/cetak_permendagri'),
                            'class' => 'btn btn-success',
                            'icon' => 'fa fa-print',
                            'text' => 'Cetak Permendagri 46',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/kegiatanpembangunan/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kegiatan Pembangunan',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Tahun Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tahun_anggaran',
                                    'placeholder' => date('Y'),
                                    'default' => date('Y')
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Volume',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'volume',
                                    'placeholder' => 'Volume Kegiatan'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Waktu',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'waktu',
                                    'placeholder' => 'Waktu Pelaksanaan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Nama Proyek/Kegiatan',
                                    'type' => 'textarea',
                                    'variable' => 'required_nama_proyek_kegiatan',
                                    'placeholder' => 'Nama Proyek/Kegiatan Lengkap',
                                    'required' => true
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana Pemerintah',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_pemerintah',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana Provinsi',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_provinsi',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana Kab/Kota',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_kab_kota',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana Swadaya',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_swadaya',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Jumlah Total',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'jumlah',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Sifat Proyek',
                                    'type' => 'select',
                                    'variable' => 'sifat_proyek',
                                    'options' => [
                                        ['value' => 'Baru', 'text' => 'Baru'],
                                        ['value' => 'Lanjutan', 'text' => 'Lanjutan']
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Pelaksana',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pelaksana',
                                    'placeholder' => 'Pelaksana'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'inventarisasihasilpembangunan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Inventarisasi Hasil-hasil Pembangunan',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'jenis_nama_hasil_pembangunan' => 'Jenis/Nama Hasil Pembangunan',
                        'volume' => 'Volume',
                        'biaya' => 'Biaya (Rp)',
                        'lokasi' => 'Lokasi',
                        'tahun_anggaran' => 'Tahun',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/inventarisasihasilpembangunan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.jenis_nama_hasil_pembangunan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/inventarisasihasilpembangunan/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('cetak/buku_inventarisasi_hasil_pembangunan'),
                            'class' => 'btn btn-success',
                            'icon' => 'fa fa-print',
                            'text' => 'Cetak Permendagri 46',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/inventarisasihasilpembangunan/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Inventarisasi Hasil-hasil Pembangunan',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tahun Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tahun_anggaran',
                                    'placeholder' => date('Y'),
                                    'default' => date('Y')
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Volume',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'volume',
                                    'placeholder' => 'Volume'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Biaya (Rp)',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'biaya',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Jenis/Nama Hasil Pembangunan',
                                    'type' => 'textarea',
                                    'variable' => 'required_jenis_nama_hasil_pembangunan',
                                    'placeholder' => 'Jenis/Nama Hasil Pembangunan Lengkap',
                                    'required' => true
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Lokasi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'lokasi',
                                    'placeholder' => 'Lokasi'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kaderpemberdayaan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kader Pendampingan dan Pemberdayaan Masyarakat',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama' => 'Nama',
                        'umur' => 'Umur',
                        'jenis_kelamin' => 'Jenis Kelamin',
                        'bidang' => 'Bidang',
                        'tahun_anggaran' => 'Tahun',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kaderpemberdayaan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/kaderpemberdayaan/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('cetak/buku_kader_pemberdayaan'),
                            'class' => 'btn btn-success',
                            'icon' => 'fa fa-print',
                            'text' => 'Cetak Permendagri 46',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/kaderpemberdayaan/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kader Pendampingan dan Pemberdayaan Masyarakat',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Tahun Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tahun_anggaran',
                                    'placeholder' => date('Y'),
                                    'default' => date('Y')
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nama',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_nama',
                                    'placeholder' => 'Nama Lengkap',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Umur',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'umur',
                                    'placeholder' => 'Umur'
                                ],
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Jenis Kelamin',
                                    'type' => 'select',
                                    'variable' => 'jenis_kelamin',
                                    'options' => [
                                        ['value' => 'Laki-laki', 'text' => 'Laki-laki'],
                                        ['value' => 'Perempuan', 'text' => 'Perempuan']
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Pendidikan/Kursus',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pendidikan_kursus',
                                    'placeholder' => 'Pendidikan/Kursus yang pernah diikuti'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Bidang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'bidang',
                                    'placeholder' => 'Bidang Keahlian'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Alamat',
                                    'type' => 'textarea',
                                    'variable' => 'alamat',
                                    'placeholder' => 'Alamat Lengkap'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'apbdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku APB Desa',
                    'table' => [
                        'tahun' => 'Tahun',
                        'pendapatan' => 'Pendapatan (Rp)',
                        'belanja' => 'Belanja (Rp)',
                        'pembiayaan' => 'Pembiayaan (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/apbdesa/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.tahun}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tahun' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku APB Desa',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tahun', 'type' => 'input', 'input_type' => 'number', 'variable' => 'required_tahun', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Pendapatan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pendapatan'],
                            ['parent_class' => 'col-md-3', 'label' => 'Belanja (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'belanja'],
                            ['parent_class' => 'col-md-3', 'label' => 'Pembiayaan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pembiayaan'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'rabdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Rencana Anggaran Biaya (RAB)',
                    'table' => [
                        'tahun' => 'Tahun',
                        'program' => 'Program',
                        'kegiatan' => 'Kegiatan',
                        'pagu' => 'Pagu (Rp)',
                        'rincian' => 'Rincian',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/rabdesa/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.program}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tahun' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku RAB',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tahun', 'type' => 'input', 'input_type' => 'number', 'variable' => 'required_tahun', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Program', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_program', 'required' => true],
                            ['parent_class' => 'col-md-5', 'label' => 'Kegiatan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kegiatan', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Pagu (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pagu'],
                            ['parent_class' => 'col-md-9', 'label' => 'Rincian', 'type' => 'textarea', 'variable' => 'rincian']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kaspembantukegiatan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kas Pembantu Kegiatan',
                    'table' => [
                        'bidang' => 'Bidang',
                        'kegiatan' => 'Kegiatan',
                        'tanggal' => 'Tanggal',
                        'penerimaan' => 'Penerimaan (Rp)',
                        'pengeluaran' => 'Pengeluaran (Rp)',
                        'saldo' => 'Saldo (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kaspembantukegiatan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.kegiatan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kas Pembantu Kegiatan',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Bidang', 'type' => 'input', 'input_type' => 'text', 'variable' => 'bidang'],
                            ['parent_class' => 'col-md-3', 'label' => 'Kegiatan', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kegiatan', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'No Bukti', 'type' => 'input', 'input_type' => 'text', 'variable' => 'no_bukti'],
                            ['parent_class' => 'col-md-4', 'label' => 'Uraian', 'type' => 'input', 'input_type' => 'text', 'variable' => 'uraian'],
                            ['parent_class' => 'col-md-2', 'label' => 'Penerimaan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'penerimaan'],
                            ['parent_class' => 'col-md-2', 'label' => 'Pengeluaran (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pengeluaran'],
                            ['parent_class' => 'col-md-2', 'label' => 'Saldo (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kasumum':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kas Umum',
                    'table' => [
                        'tanggal' => 'Tanggal',
                        'kode_rekening' => 'Kode Rekening',
                        'uraian' => 'Uraian',
                        'penerimaan' => 'Penerimaan (Rp)',
                        'pengeluaran' => 'Pengeluaran (Rp)',
                        'saldo' => 'Saldo (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kasumum/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.kode_rekening}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kas Umum',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Kode Rekening', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_kode_rekening', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Uraian', 'type' => 'input', 'input_type' => 'text', 'variable' => 'uraian'],
                            ['parent_class' => 'col-md-3', 'label' => 'No Bukti', 'type' => 'input', 'input_type' => 'text', 'variable' => 'no_bukti'],
                            ['parent_class' => 'col-md-3', 'label' => 'Penerimaan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'penerimaan'],
                            ['parent_class' => 'col-md-3', 'label' => 'Pengeluaran (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pengeluaran'],
                            ['parent_class' => 'col-md-3', 'label' => 'Saldo (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'kaspembantu':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Kas Pembantu',
                    'table' => [
                        'tanggal' => 'Tanggal',
                        'uraian' => 'Uraian',
                        'pemotongan' => 'Pemotongan (Rp)',
                        'penyetoran' => 'Penyetoran (Rp)',
                        'saldo' => 'Saldo (Rp)',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kaspembantu/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.uraian}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.tanggal' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Kas Pembantu',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-3', 'label' => 'Tanggal', 'type' => 'input', 'input_type' => 'date', 'variable' => 'required_tanggal', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Uraian', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_uraian', 'required' => true],
                            ['parent_class' => 'col-md-2', 'label' => 'Pemotongan (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'pemotongan'],
                            ['parent_class' => 'col-md-2', 'label' => 'Penyetoran (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'penyetoran'],
                            ['parent_class' => 'col-md-1', 'label' => 'Saldo (Rp)', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'bankdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Bank Desa',
                    'table' => [
                        'bulan' => 'Bulan',
                        'bank_cabang' => 'Bank/Cabang',
                        'rekening' => 'No Rekening',
                        'saldo_awal' => 'Saldo Awal',
                        'debet' => 'Debet',
                        'kredit' => 'Kredit',
                        'saldo' => 'Saldo',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/bankdesa/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.rekening}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.id' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Bank Desa',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-4', 'label' => 'Bulan', 'type' => 'input', 'input_type' => 'month', 'variable' => 'required_bulan', 'required' => true],
                            ['parent_class' => 'col-md-4', 'label' => 'Bank/Cabang', 'type' => 'input', 'input_type' => 'text', 'variable' => 'bank_cabang'],
                            ['parent_class' => 'col-md-4', 'label' => 'No Rekening', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_rekening', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Saldo Awal', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo_awal'],
                            ['parent_class' => 'col-md-3', 'label' => 'Debet', 'type' => 'input', 'input_type' => 'number', 'variable' => 'debet'],
                            ['parent_class' => 'col-md-3', 'label' => 'Kredit', 'type' => 'input', 'input_type' => 'number', 'variable' => 'kredit'],
                            ['parent_class' => 'col-md-3', 'label' => 'Saldo', 'type' => 'input', 'input_type' => 'number', 'variable' => 'saldo'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            break;

        case 'musyawarahdesa':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Kegiatan Musyawarah Desa',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'waktu_pelaksanaan' => 'Waktu Pelaksanaan',
                        'peserta' => 'Peserta',
                        'pembahasan' => 'Pembahasan',
                        'kesimpulan_hasil_musyawarah' => 'Kesimpulan/Hasil',
                        'tahun_anggaran' => 'Tahun',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/musyawarahdesa/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.pembahasan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/musyawarahdesa/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('cetak/kegiatan_musyawarah_desa'),
                            'class' => 'btn btn-success',
                            'icon' => 'fa fa-print',
                            'text' => 'Cetak Permendagri 46',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/musyawarahdesa/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Kegiatan Musyawarah Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-2',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Waktu Pelaksanaan',
                                    'type' => 'input',
                                    'input_type' => 'datetime-local',
                                    'variable' => 'required_waktu_pelaksanaan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-5',
                                    'label' => 'Peserta',
                                    'type' => 'textarea',
                                    'variable' => 'peserta',
                                    'placeholder' => 'Daftar peserta musyawarah'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Pembahasan',
                                    'type' => 'textarea',
                                    'variable' => 'required_pembahasan',
                                    'placeholder' => 'Pembahasan dalam musyawarah',
                                    'required' => true
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Kesimpulan/Hasil Musyawarah',
                                    'type' => 'textarea',
                                    'variable' => 'kesimpulan_hasil_musyawarah',
                                    'placeholder' => 'Kesimpulan atau hasil dari musyawarah'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Upload Berita Acara',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'berita_acara_file',
                                    'accept' => '.pdf,.doc,.docx'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Upload Notulen',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'notulen_file',
                                    'accept' => '.pdf,.doc,.docx'
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Upload Dokumentasi',
                                    'type' => 'input',
                                    'input_type' => 'file',
                                    'variable' => 'dokumentasi_file',
                                    'accept' => '.jpg,.jpeg,.png,.pdf'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'lembagakemasyarakatan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Kegiatan Lembaga Kemasyarakatan Desa',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'tanggal' => 'Tanggal',
                        'uraian_kegiatan' => 'Uraian Kegiatan',
                        'hasil_kegiatan' => 'Hasil Kegiatan',
                        'tahun_anggaran' => 'Tahun',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/lembagakemasyarakatan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.uraian_kegiatan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/lembagakemasyarakatan/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('cetak/kegiatan_lembaga_kemasyarakatan'),
                            'class' => 'btn btn-success',
                            'icon' => 'fa fa-print',
                            'text' => 'Cetak Permendagri 46',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/lembagakemasyarakatan/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Kegiatan Lembaga Kemasyarakatan Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tanggal',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'required_tanggal',
                                    'required' => true
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Uraian Kegiatan',
                                    'type' => 'textarea',
                                    'variable' => 'required_uraian_kegiatan',
                                    'placeholder' => 'Uraian kegiatan lembaga kemasyarakatan',
                                    'required' => true
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Hasil Kegiatan',
                                    'type' => 'textarea',
                                    'variable' => 'hasil_kegiatan',
                                    'placeholder' => 'Hasil dari kegiatan yang dilaksanakan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'kabupaten':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Data Kabupaten',
                    'table' => [
                        'nama_kabupaten' => 'Nama Kabupaten',
                        'kode_kabupaten' => 'Kode',
                        'provinsi' => 'Provinsi',
                        'alamat' => 'Alamat',
                        'keterangan' => 'Keterangan',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/kabupaten/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'a', 'href' => base_url('master/kabupaten/detail/${table.primary}'), 'class' => 'btn btn-info btn-sm', 'icon' => 'fa fa-eye'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_kabupaten}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['id' => 'DESC']]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Data Kabupaten',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'input', 'input_type' => 'hidden', 'variable' => 'userid', 'default' => getCurrentIdUser()],
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-6', 'label' => 'Nama Kabupaten', 'type' => 'input', 'input_type' => 'text', 'variable' => 'required_nama_kabupaten', 'required' => true],
                            ['parent_class' => 'col-md-3', 'label' => 'Kode Kabupaten', 'type' => 'input', 'input_type' => 'text', 'variable' => 'kode_kabupaten'],
                            ['parent_class' => 'col-md-3', 'label' => 'Provinsi', 'type' => 'input', 'input_type' => 'text', 'variable' => 'provinsi'],
                            ['parent_class' => 'col-md-12', 'label' => 'Alamat', 'type' => 'input', 'input_type' => 'text', 'variable' => 'alamat'],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'textarea', 'variable' => 'keterangan']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)],
                        ['type' => 'button', 'class' => 'btn btn-primary', 'text' => 'Simpan']
                    ]
                );
            }
            if ($name == 'detail') {
                $output = array(
                    'title' => 'Detail Kabupaten',
                    'row_data' => $row,
                    'fields' => [
                        ['type' => 'row', 'data' => [
                            ['parent_class' => 'col-md-6', 'label' => 'Nama Kabupaten', 'type' => 'text', 'value' => $row->nama_kabupaten ?? ''],
                            ['parent_class' => 'col-md-3', 'label' => 'Kode Kabupaten', 'type' => 'text', 'value' => $row->kode_kabupaten ?? ''],
                            ['parent_class' => 'col-md-3', 'label' => 'Provinsi', 'type' => 'text', 'value' => $row->provinsi ?? ''],
                            ['parent_class' => 'col-md-12', 'label' => 'Alamat', 'type' => 'text', 'value' => $row->alamat ?? ''],
                            ['parent_class' => 'col-md-12', 'label' => 'Keterangan', 'type' => 'text', 'value' => $row->keterangan ?? '']
                        ]]
                    ],
                    'footer_button' => [
                        ['type' => 'a', 'class' => 'btn btn-danger', 'text' => 'Kembali', 'href' => base_url('master/' . $feature)]
                    ]
                );
            }
            break;

        case 'pendapatanbelanjapembiayaan':
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Pendapatan - Belanja - Pembiayaan',
                    'table' => [
                        'kode_kegiatan' => 'Kode Kegiatan',
                        'bidang' => 'Bidang',
                        'nama_kegiatan' => 'Nama Kegiatan',
                        'jenis_anggaran' => 'Jenis',
                        'pagu_anggaran' => 'Pagu Anggaran',
                        'realisasi_anggaran' => 'Realisasi',
                        'persentase_realisasi' => '% Realisasi',
                        'tahun_anggaran' => 'Tahun',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/pendapatanbelanjapembiayaan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_kegiatan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.kode_kegiatan' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/pendapatanbelanjapembiayaan/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/pendapatanbelanjapembiayaan/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/pendapatanbelanjapembiayaan/controlling'),
                            'class' => 'btn btn-warning',
                            'icon' => 'fa fa-calculator',
                            'text' => 'Controlling'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Pendapatan - Belanja - Pembiayaan',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Kode Kegiatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_kode_kegiatan',
                                    'placeholder' => 'Kode Kegiatan',
                                    'required' => true
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Bidang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'bidang',
                                    'placeholder' => 'Bidang'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sub Bidang',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'sub_bidang',
                                    'placeholder' => 'Sub Bidang'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Kegiatan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'kegiatan',
                                    'placeholder' => 'Kegiatan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Nama Kegiatan',
                                    'type' => 'textarea',
                                    'variable' => 'nama_kegiatan',
                                    'placeholder' => 'Nama Kegiatan Lengkap'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Jenis Anggaran',
                                    'type' => 'select',
                                    'variable' => 'jenis_anggaran',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Pendapatan', 'name' => 'Pendapatan'],
                                            ['id' => 'Belanja', 'name' => 'Belanja'],
                                            ['id' => 'Pembiayaan', 'name' => 'Pembiayaan']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->jenis_anggaran : 'Belanja'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tahun Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tahun_anggaran',
                                    'placeholder' => date('Y'),
                                    'default' => date('Y')
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pagu Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'pagu_anggaran',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pagu Perubahan Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'pagu_perubahan_anggaran',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Realisasi Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'realisasi_anggaran',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Prioritas Dana Desa',
                                    'type' => 'select',
                                    'variable' => 'prioritas_dana_desa',
                                    'defaultvalue' => [
                                        'data' => json_decode(json_encode([
                                            ['id' => 'Tidak', 'name' => 'Tidak'],
                                            ['id' => 'Ya', 'name' => 'Ya']
                                        ])),
                                        'value' => 'id',
                                        'text' => 'name',
                                        'selected' => $row != null ? $row->prioritas_dana_desa : 'Tidak'
                                    ]
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana PAD',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_pad',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana ADD',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_add',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana DDS',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_dds',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana PBH',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_pbh',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana PBK',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_pbk',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana PBP',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_pbp',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana SWD',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_swd',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Dana DLL',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_dana_dll',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan Dana Murni',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'keterangan_dana_murni',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Keterangan Dana Silpa',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'keterangan_dana_silpa',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan Tambahan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan_tambahan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            }
            if ($name == 'index') {
                $output = array(
                    'title' => 'Buku Rencana Kerja Pembangunan Desa',
                    'table' => [
                        'nomor_urut' => 'No. Urut',
                        'nama_proyek_kegiatan' => 'Nama Proyek/Kegiatan',
                        'lokasi' => 'Lokasi',
                        'jumlah' => 'Jumlah (Rp)',
                        'pelaksana' => 'Pelaksana',
                        'tahun_anggaran' => 'Tahun',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                ['type' => 'a', 'href' => base_url('master/rencanakerjapembangunan/edit/${table.primary}'), 'class' => 'btn btn-primary btn-sm', 'icon' => 'fa fa-edit'],
                                ['type' => 'button', 'onclick' => 'deleteData(\'${table.value.nama_proyek_kegiatan}\', ${table.primary})', 'class' => 'btn btn-danger btn-sm', 'icon' => 'fa fa-trash']
                            ]
                        ]
                    ],
                    'database' => ['method' => 'result', 'order_by' => ['a.nomor_urut' => 'ASC']],
                    'header_button' => [
                        [
                            'type' => 'a',
                            'href' => base_url('master/rencanakerjapembangunan/export'),
                            'class' => 'btn btn-danger',
                            'icon' => 'fa fa-file-pdf-o',
                            'text' => 'Export PDF',
                            'target' => '_blank'
                        ],
                        [
                            'type' => 'a',
                            'href' => base_url('master/rencanakerjapembangunan/dashboard'),
                            'class' => 'btn btn-info',
                            'icon' => 'fa fa-dashboard',
                            'text' => 'Dashboard'
                        ]
                    ]
                );
            }
            if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'title' => 'Buku Rencana Kerja Pembangunan Desa',
                    'row_data' => $row,
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'userid',
                            'default' => getCurrentIdUser()
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Nomor Urut',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'nomor_urut',
                                    'placeholder' => 'Auto'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Tahun Anggaran',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'tahun_anggaran',
                                    'placeholder' => date('Y'),
                                    'default' => date('Y')
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Lokasi',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'lokasi',
                                    'placeholder' => 'Lokasi Kegiatan'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Pelaksana',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'pelaksana',
                                    'placeholder' => 'Pelaksana'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Nama Proyek/Kegiatan',
                                    'type' => 'textarea',
                                    'variable' => 'required_nama_proyek_kegiatan',
                                    'placeholder' => 'Nama Proyek/Kegiatan Lengkap',
                                    'required' => true
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Biaya Pemerintah',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_biaya_pemerintah',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Biaya Provinsi',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_biaya_provinsi',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Biaya Kab/Kota',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_biaya_kab_kota',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ],
                                [
                                    'parent_class' => 'col-md-3',
                                    'label' => 'Sumber Biaya Swadaya',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'sumber_biaya_swadaya',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-6',
                                    'label' => 'Jumlah Total',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'jumlah',
                                    'placeholder' => '0',
                                    'step' => '0.01'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Manfaat',
                                    'type' => 'textarea',
                                    'variable' => 'manfaat',
                                    'placeholder' => 'Manfaat dari proyek/kegiatan'
                                ]
                            ]
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Keterangan',
                                    'type' => 'textarea',
                                    'variable' => 'keterangan',
                                    'placeholder' => 'Keterangan tambahan'
                                ]
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature)
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        default:
            break;
    }

    return $output;
}

function getFieldParameter($feature, $name)
{
    $variable = array();
    foreach (getContents($feature, $name)['fields'] as $value) {
        if (isset($value['variable'])) {
            if (isset($value['required'])) {
                $variable[] = $value['variable'];
            }
        } else if (isset($value['data'])) {
            foreach ($value['data'] as $v) {
                if (isset($v['required'])) {
                    $variable[] = $v['variable'];
                }
            }
        }
    }

    return $variable;
}

function stringEncryption($action, $string)
{
    $output = false;

    $encrypt_method = 'AES-256-CBC'; // Default
    $secret_key = 'karpeldedvtech'; // Change the key!
    $secret_iv = 'owr216he890';  // Change the init vector!

    // hash
    $key = hash('sha256', $secret_key);

    // iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a warning
    $iv = substr(hash('sha256', $secret_iv), 0, 16);

    if ($action == 'encrypt') {
        $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);
    } else if ($action == 'decrypt') {
        $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
    }

    return $output;
}

function validateDate($dateStr, $format)
{
    date_default_timezone_set('UTC');
    if ($dateStr != null) {
        $date = DateTime::createFromFormat($format, $dateStr);
        return $date && ($date->format($format) === $dateStr);
    } else {
        return false;
    }
}

function tgl_indo($tanggal)
{
    $bulan = array(
        'Januari',
        'Februari',
        'Maret',
        'April',
        'Mei',
        'Juni',
        'Juli',
        'Agustus',
        'September',
        'Oktober',
        'November',
        'Desember'
    );

    $pecahkan = explode('-', $tanggal);

    if (isset($pecahkan[0]) && isset($pecahkan[1]) && isset($pecahkan[2])) {
        return $pecahkan[2] . ' ' . $bulan[(int)$pecahkan[1] - 1] . ' ' . $pecahkan[0];
    } else {
        return date('d F Y', strtotime($tanggal));
    }
}

function isAdmin()
{
    return getSessionValue('ROLE') == 'Admin';
}

function isBPD()
{
    return getSessionValue('ROLE') == 'BPD';
}

function isVillages()
{
    return getSessionValue('ROLE') == 'Villages';
}

function isOperator()
{
    return getSessionValue('ROLE') == 'Operator';
}

function isKecamatan()
{
    return getSessionValue('ROLE') == 'Kecamatan';
}

function isPMD()
{
    return getSessionValue('ROLE') == 'DPMD';
}

function isKepalaDesa()
{
    return getSessionValue('ROLE') == 'Kepala Desa';
}

function getConfigWablas($key)
{
    $CI = &get_instance();

    $get = $CI->db->get('configwablas')->row();

    return $get != null ? $get->$key : null;
}

function isOnlineWABlas()
{
    $phonenumber = getConfigWablas('phonenumber');
    $token = getConfigWablas('apikey');
    $domain = getConfigWablas('domain');

    $curl = curl_init();

    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        "Authorization: $token",
        "url: $domain"
    ));
    curl_setopt($curl, CURLOPT_URL,  "https://phone.wablas.com/check-phone-number?phones=$phonenumber");
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);

    $result = curl_exec($curl);
    curl_close($curl);

    return json_decode($result);
}

function sendWABlas($phonenumber, $message)
{
    $curl = curl_init();
    $secret_key = "wRD3WqKN";
    $token = getConfigWablas('apikey') . '.' . $secret_key;

    $data = [
        'phone' => $phonenumber,
        'message' => $message,
    ];

    $url_send = getConfigWablas('domain') . "api/send-message";

    curl_setopt($curl, CURLOPT_HTTPHEADER, array("Authorization: $token",));
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_URL, $url_send);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);

    curl_exec($curl);
    curl_close($curl);
}

function getCurrentProfile()
{
    return base_url('assets/img/avatar/avatar-1.png');
}

function syncSiades($apikey, $type = 'BPD')
{
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://siades.id/api/thirdparty/userapps',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => 'apikey=' . $apikey . '&type=' . $type,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded',
        ),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    return json_decode($response);
}

function asset_url($filename)
{
    $asseturl = "https://storage.googleapis.com/siades/$filename";

    $headers = get_headers($asseturl);
    $status = substr($headers[0], 9, 3);

    if ($status == '200') {
        return $asseturl;
    } else {
        return base_url('assets/img/avatars/1.png');
    }
}

function generateRandomString($length = 10)
{
    // create variable $characters with value '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    // create variable $charactersLength with value strlen($characters)
    $charactersLength = strlen($characters);
    // create variable $randomString with value ''
    $randomString = '';
    // create loop for generate random string
    for ($i = 0; $i < $length; $i++) {
        // create variable $randomString with value $characters[rand(0, $charactersLength - 1)]
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    // return $randomString
    return $randomString;
}

// create function to get day name indonesia    
function getDayIndo($date)
{
    $day = date('D', strtotime($date));

    switch ($day) {
        case 'Sun':
            $hari = "Minggu";
            break;

        case 'Mon':
            $hari = "Senin";
            break;

        case 'Tue':
            $hari = "Selasa";
            break;

        case 'Wed':
            $hari = "Rabu";
            break;

        case 'Thu':
            $hari = "Kamis";
            break;

        case 'Fri':
            $hari = "Jumat";
            break;

        case 'Sat':
            $hari = "Sabtu";
            break;

        default:
            $hari = "Tidak di ketahui";
            break;
    }

    return $hari;
}

function getKabupaten_Gides()
{
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://desa.gides.id/master/select/kabupaten',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => 'provinsi=22',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded',
        ),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    return $response;
}

function getKecamatan_Gides($kabupaten)
{
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://desa.gides.id/master/select/kecamatan',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => 'kabupaten=' . $kabupaten,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded',
        ),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    return  $response;
}

function insertLogactivity($action = 'delete', $description = 'delete data', $data = [])
{
    $ci = &get_instance();

    $ci->db->insert('logactivity', array(
        'action' => $action,
        'description' => $description,
        'data' => json_encode($data),
        'createdby' => getCurrentIdUser(),
        'createddate' => getCurrentDate()
    ));
}

function downloadFile($url)
{
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    $filename = 'application/cache/' . time() . '.png';
    file_put_contents($filename, $response);

    $base64 = base64_encode(file_get_contents($filename));
    unlink($filename);

    return 'data:image/png;base64,' . $base64;
}

function getTotalVillagesByCity($cityname)
{
    $CI = &get_instance();

    return $CI->db->select('COUNT(*) AS total')
        ->from('msusers')
        ->where('kabkotaname', $cityname)
        ->where('role', 'Villages')
        ->get()
        ->row()->total;
}

function getTotalVillagesByDistrict($districtname)
{
    $CI = &get_instance();

    return $CI->db->select('COUNT(*) AS total')
        ->from('msusers')
        ->where('kecamatanname', $districtname)
        ->where('role', 'Villages')
        ->get()
        ->row()->total;
}
