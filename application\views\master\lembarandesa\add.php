<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('master/lembarandesa'); ?>">Lembaran & Berita Desa</a></li>
                    <li class="breadcrumb-item" aria-current="page">Tambah</li>
                </ul>
            </div>
            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Tambah Lembaran & Berita Desa</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form id="frmAddLembaranDesa" action="<?= base_url('master/lembarandesa/process_add') ?>" method="POST" autocomplete="off">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jenis" class="form-label">Jenis <span class="text-danger">*</span></label>
                                <select name="jenis" class="form-control" required>
                                    <option value="">Pilih Jenis</option>
                                    <option value="Lembaran Desa">Lembaran Desa</option>
                                    <option value="Berita Desa">Berita Desa</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nomor_ditetapkan" class="form-label">Nomor Ditetapkan <span class="text-danger">*</span></label>
                                <input type="text" name="nomor_ditetapkan" class="form-control" placeholder="Masukkan nomor ditetapkan" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tanggal_ditetapkan" class="form-label">Tanggal Ditetapkan <span class="text-danger">*</span></label>
                                <input type="date" name="tanggal_ditetapkan" class="form-control" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tentang" class="form-label">Tentang <span class="text-danger">*</span></label>
                                <input type="text" name="tentang" class="form-control" placeholder="Masukkan tentang" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nomor_diundangkan" class="form-label">Nomor Diundangkan</label>
                                <input type="text" name="nomor_diundangkan" class="form-control" placeholder="Masukkan nomor diundangkan">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tanggal_diundangkan" class="form-label">Tanggal Diundangkan</label>
                                <input type="date" name="tanggal_diundangkan" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="keterangan" class="form-label">Keterangan</label>
                                <textarea name="keterangan" class="form-control" rows="4" placeholder="Masukkan keterangan"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <a href="<?= base_url('master/lembarandesa') ?>" class="btn btn-danger">Kembali</a>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Handle form submission
        $.AjaxRequest('#frmAddLembaranDesa', {
            success: function(response) {
                if (response.RESULT == 'SUCCESS') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.href = '<?= base_url('master/lembarandesa') ?>';
                    });
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    });
</script>