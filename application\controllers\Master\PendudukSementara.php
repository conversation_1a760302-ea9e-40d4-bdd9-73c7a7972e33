<?php

use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property PendudukSementaras $penduduksementaras
 * @property Superadmins $admins
 */
class PendudukSementara extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('PendudukSementaras', 'penduduksementaras');
        $this->load->model('Superadmins', 'admins');
    }

    public function index()
    {
        $tahun = getGet('tahun');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($tahun != null) {
            $where['YEAR(a.datang_tanggal)'] = $tahun;
        }

        $data = array();
        $data['title'] = 'Buku Penduduk Sementara';
        $data['content'] = 'master/penduduksementara/index';
        $data['tahun'] = $tahun;

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $data['penduduksementara'] = $this->penduduksementaras->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        return $this->load->view('master', $data);
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Get filter parameters
        $tahun = getGet('tahun');

        $where = array();

        if ($tahun != null) {
            $where['YEAR(a.datang_tanggal)'] = $tahun;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $penduduk = $this->penduduksementaras->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        $html2pdf = new Html2Pdf('L');
        $html2pdf->writeHTML($this->load->view('master/penduduksementara/export', array(
            'penduduk' => $penduduk,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $this->penduduksementaras->getStatistics(getCurrentUser()->id ?? null)
        ), true));
        $html2pdf->output('Buku Penduduk Sementara.pdf');
    }

    public function statistics()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $statistics = $this->penduduksementaras->getStatistics($userid);

        return JSONResponseDefault('SUCCESS', $statistics);
    }

    public function search()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $keyword = $this->input->get('q');
        if (empty($keyword)) {
            return JSONResponseDefault('SUCCESS', []);
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $results = $this->penduduksementaras->search($keyword, $userid);

        $data = [];
        foreach ($results as $row) {
            $data[] = [
                'id' => $row->id,
                'nama_lengkap' => $row->nama_lengkap,
                'nomor_identitas' => $row->nomor_identitas,
                'jenis_kelamin' => $row->jenis_kelamin,
                'kebangsaan' => $row->kebangsaan,
                'datang_dari' => $row->datang_dari,
                'text' => $row->nama_lengkap . ' - ' . $row->nomor_identitas . ' (' . $row->kebangsaan . ')'
            ];
        }

        return JSONResponseDefault('SUCCESS', $data);
    }

    public function dashboard()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'Dashboard Buku Penduduk Sementara';
        $data['content'] = 'master/penduduksementara/dashboard';
        $data['statistics'] = $this->penduduksementaras->getStatistics($userid);
        $data['recent_items'] = $this->penduduksementaras->select('*')
            ->where($userid ? ['userid' => $userid] : [])
            ->order_by('createddate', 'DESC')
            ->limit(10)
            ->result();

        return $this->load->view('master', $data);
    }

    public function chart_data()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $type = $this->input->get('type', true);
        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        switch ($type) {
            case 'by_month':
                $statistics = $this->penduduksementaras->getStatistics($userid);
                $data = $statistics['by_month'];
                break;
            case 'by_gender':
                $statistics = $this->penduduksementaras->getStatistics($userid);
                $data = $statistics['by_gender'];
                break;
            case 'by_kebangsaan':
                $statistics = $this->penduduksementaras->getStatistics($userid);
                $data = $statistics['by_kebangsaan'];
                break;
            case 'by_age':
                $statistics = $this->penduduksementaras->getStatistics($userid);
                $data = $statistics['by_age'];
                break;
            case 'recent_arrivals':
                $statistics = $this->penduduksementaras->getStatistics($userid);
                $data = $statistics['recent_arrivals'];
                break;
            default:
                $data = [];
        }

        return JSONResponseDefault('SUCCESS', $data);
    }

    public function import()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Import Data Penduduk Sementara';
        $data['content'] = 'master/penduduksementara/import';

        return $this->load->view('master', $data);
    }

    public function process_import()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (empty($_FILES['file']['name'])) {
            return JSONResponseDefault('FAILED', 'File Excel harus dipilih');
        }

        $config['upload_path'] = './uploads/temp/';
        $config['allowed_types'] = 'xlsx|xls';
        $config['max_size'] = 5120; // 5MB
        $config['file_name'] = 'import_penduduk_sementara_' . time() . '_' . $_FILES['file']['name'];

        // Create directory if not exists
        if (!is_dir('./uploads/temp/')) {
            mkdir('./uploads/temp/', 0755, true);
        }

        $this->load->library('upload', $config);

        if (!$this->upload->do_upload('file')) {
            return JSONResponseDefault('FAILED', 'Error upload file: ' . $this->upload->display_errors());
        }

        $upload_data = $this->upload->data();
        $file_path = $upload_data['full_path'];

        try {
            // Load PhpSpreadsheet
            require_once FCPATH . 'vendor/autoload.php';

            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
            $spreadsheet = $reader->load($file_path);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            $imported = 0;
            $errors = [];
            $userid = getCurrentUser()->id;

            // Skip header row
            for ($i = 1; $i < count($rows); $i++) {
                $row = $rows[$i];

                if (empty($row[1])) continue; // Skip if nama lengkap empty

                // Check if nomor identitas already exists
                if (!empty($row[3]) && $this->penduduksementaras->isNomorIdentitasExists($row[3])) {
                    $errors[] = "Baris " . ($i + 1) . ": Nomor Identitas sudah terdaftar";
                    continue;
                }

                // Calculate age if tanggal_lahir provided
                $umur = null;
                if (!empty($row[5])) {
                    $umur = $this->penduduksementaras->calculateAge($row[5]);
                }

                $data = [
                    'nomor_urut' => $row[0] ?: $this->penduduksementaras->getNextNomorUrut($userid),
                    'nama_lengkap' => $row[1],
                    'jenis_kelamin' => $row[2],
                    'nomor_identitas' => $row[3],
                    'tempat_lahir' => $row[4],
                    'tanggal_lahir' => !empty($row[5]) ? date('Y-m-d', strtotime($row[5])) : null,
                    'umur' => $umur,
                    'pekerjaan' => $row[7],
                    'kebangsaan' => $row[8],
                    'keturunan' => $row[9],
                    'datang_dari' => $row[10],
                    'maksud_tujuan_kedatangan' => $row[11],
                    'nama_alamat_didatangi' => $row[12],
                    'datang_tanggal' => !empty($row[13]) ? date('Y-m-d', strtotime($row[13])) : null,
                    'pergi_tanggal' => !empty($row[14]) ? date('Y-m-d', strtotime($row[14])) : null,
                    'keterangan' => $row[15],
                    'userid' => $userid,
                    'createdby' => $userid,
                    'createddate' => date('Y-m-d H:i:s')
                ];

                if ($this->penduduksementaras->insert($data)) {
                    $imported++;
                } else {
                    $errors[] = "Baris " . ($i + 1) . ": Gagal import data";
                }
            }

            // Delete uploaded file
            unlink($file_path);

            $message = "Berhasil import $imported data";
            if (!empty($errors)) {
                $message .= ". Error: " . implode(', ', array_slice($errors, 0, 5));
            }

            return JSONResponseDefault('SUCCESS', $message);
        } catch (Exception $e) {
            // Delete uploaded file if exists
            if (file_exists($file_path)) {
                unlink($file_path);
            }

            return JSONResponseDefault('FAILED', 'Error processing file: ' . $e->getMessage());
        }
    }

    public function export_excel()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $where = array();
        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['userid'] = $currentuser->id;
        }

        $data = $this->penduduksementaras->exportToArray($where);

        // Load PhpSpreadsheet
        require_once FCPATH . 'vendor/autoload.php';

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $headers = array_keys($data[0] ?? []);
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $col++;
        }

        // Set data
        $row = 2;
        foreach ($data as $item) {
            $col = 'A';
            foreach ($item as $value) {
                $sheet->setCellValue($col . $row, $value);
                $col++;
            }
            $row++;
        }

        // Set filename and download
        $filename = 'Buku_Penduduk_Sementara_' . date('Y-m-d_H-i-s') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }
}
