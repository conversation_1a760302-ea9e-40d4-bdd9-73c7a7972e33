<?php

use Dompdf\Dompdf;
use Dompdf\Options;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property PendudukSementaras $penduduksementaras
 * @property Superadmins $admins
 */
class PendudukSementara extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('PendudukSementaras', 'penduduksementaras');
        $this->load->model('Superadmins', 'admins');
    }

    public function index()
    {
        $tahun = getGet('tahun');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($tahun != null) {
            $where['YEAR(a.datang_tanggal)'] = $tahun;
        }

        $data = array();
        $data['title'] = 'Buku Penduduk Sementara';
        $data['content'] = 'master/penduduksementara/index';
        $data['tahun'] = $tahun;

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $data['penduduksementara'] = $this->penduduksementaras->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        return $this->load->view('master', $data);
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Get filter parameters
        $tahun = getGet('tahun');

        $where = array();

        if ($tahun != null) {
            $where['YEAR(a.datang_tanggal)'] = $tahun;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $penduduk = $this->penduduksementaras->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        // Get statistics with error handling
        try {
            $userid = null;
            if (isVillages() || isBPD()) {
                $userid = getCurrentUser()->id;
            }
            $statistics = $this->penduduksementaras->getStatistics($userid);
        } catch (Exception $e) {
            // If statistics fail, use empty array
            $statistics = [
                'totals' => (object)['total_penduduk' => 0],
                'currently_staying' => (object)['masih_tinggal' => 0],
                'already_left' => (object)['sudah_pergi' => 0],
                'by_gender' => [],
                'by_kebangsaan' => [],
                'by_month' => [],
                'recent_arrivals' => [],
                'by_age' => []
            ];
        }

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/penduduksementara/export', array(
            'penduduk' => $penduduk,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $statistics
        ), true));
        $dompdf->render();
        $dompdf->stream('Buku Penduduk Sementara.pdf', array("Attachment" => false));
    }
}
