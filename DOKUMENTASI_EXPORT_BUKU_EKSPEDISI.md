# Dokumentasi Export Buku Ekspedisi

## Perubahan yang Dilakukan

### 1. **Format Tabel Export PDF** ✅
File: `application/views/master/ekspedisi/export.php`

**Perubahan:**
- Mengubah format tabel dari struktur buku agenda menjadi format buku ekspedisi yang sesuai
- Struktur kolom baru sesuai permintaan:
  1. **NOMOR URUT** (25mm)
  2. **TANGGAL PENGIRIMAN** (40mm)
  3. **TANGGAL DAN NOMOR SURAT** (60mm)
  4. **ISI SINGKAT SURAT YANG DIKIRIM** (80mm)
  5. **DITUJUKAN KEPADA** (50mm)
  6. **KETERANGAN** (18mm)

### 2. **Mapping Data dari Database** ✅
**Field mapping:**
- **Nomor Urut**: `ekspedisi.nomor_urut`
- **Tanggal Pengiriman**: `ekspedisi.tanggal_pengiriman`
- **Tanggal dan Nomor Surat**: 
  - `ekspedisi.tanggal_surat` (jika ada, ditampilkan di baris pertama)
  - `ekspedisi.nomor_surat` (ditampilk<PERSON> di baris kedua atau tunggal)
- **Isi Singkat Surat**: `ekspedisi.isi_singkat_surat`
- **Ditujukan Kepada**: `ekspedisi.ditujukan_kepada`
- **Keterangan**: `ekspedisi.keterangan`

### 3. **Layout dan Styling** ✅
- **Paper Size**: A4 Landscape
- **Total Width**: 273mm (sesuai area cetak landscape)
- **Font Size**: 12px untuk header, 10px untuk data
- **Border**: 0.5px solid black untuk semua cell
- **Text Alignment**: Center untuk header dan nomor urut, left untuk data lainnya

## Struktur Tabel Baru

```
|NOMOR|TANGGAL     |TANGGAL DAN      |ISI SINGKAT SURAT    |DITUJUKAN|KETERANGAN|
|URUT |PENGIRIMAN  |NOMOR SURAT      |YANG DIKIRIM         |KEPADA   |          |
|-----|------------|-----------------|---------------------|---------|----------|
|  1  | 01/01/2024 | 31/12/2023      | Undangan rapat      | Kepala  | Urgent   |
|     |            | 001/SK/2023     | koordinasi          | Dinas   |          |
|  2  | 02/01/2024 | 002/SK/2024     | Laporan kegiatan    | Camat   | -        |
```

## Format Data

### Kolom "Tanggal dan Nomor Surat"
- Jika `tanggal_surat` tersedia: ditampilkan di baris pertama dengan format `dd/mm/yyyy`
- `nomor_surat` ditampilkan di baris kedua (atau baris tunggal jika tidak ada tanggal surat)
- Menggunakan `<br>` untuk pemisah baris

### Format Tanggal
- **Tanggal Pengiriman**: Format `dd/mm/yyyy`
- **Tanggal Surat**: Format `dd/mm/yyyy`
- Jika tanggal kosong/null: ditampilkan sebagai "-"

### Handling Data Kosong
- Semua field yang kosong/null ditampilkan sebagai "-"
- Menggunakan operator `?:` untuk fallback value

## Cara Penggunaan

### 1. **Export PDF**
- Akses halaman: `/master/ekspedisi`
- Pilih filter tanggal jika diperlukan
- Klik tombol "Export"
- PDF akan di-generate dengan format tabel baru

### 2. **Filter Data Export**
- Export mendukung filter tanggal dari halaman index
- Parameter: `startdate` dan `enddate`
- Filter berdasarkan `tanggal_pengiriman`

### 3. **Contoh URL Export**
```
/master/ekspedisi/export
/master/ekspedisi/export?startdate=2024-01-01&enddate=2024-01-31
```

## Kode Implementation

### Header Tabel
```html
<tr>
    <th style="text-align: center;">NOMOR URUT</th>
    <th style="text-align: center;">TANGGAL PENGIRIMAN</th>
    <th style="text-align: center;">TANGGAL DAN NOMOR SURAT</th>
    <th style="text-align: center;">ISI SINGKAT SURAT YANG DIKIRIM</th>
    <th style="text-align: center;">DITUJUKAN KEPADA</th>
    <th style="text-align: center;">KETERANGAN</th>
</tr>
```

### Data Row
```php
<?php foreach ($ekspedisi as $item): ?>
    <tr>
        <td style="text-align: center;"><?= $item->nomor_urut ?? '-' ?></td>
        <td style="text-align: center;"><?= $item->tanggal_pengiriman ? date('d/m/Y', strtotime($item->tanggal_pengiriman)) : '-' ?></td>
        <td style="font-size: 10px;">
            <?php if ($item->tanggal_surat): ?>
                <?= date('d/m/Y', strtotime($item->tanggal_surat)) ?><br>
            <?php endif; ?>
            <?= $item->nomor_surat ?: '-' ?>
        </td>
        <td style="font-size: 10px;"><?= $item->isi_singkat_surat ?: '-' ?></td>
        <td style="font-size: 10px;"><?= $item->ditujukan_kepada ?: '-' ?></td>
        <td style="font-size: 10px;"><?= $item->keterangan ?: '-' ?></td>
    </tr>
<?php endforeach; ?>
```

## Testing

### 1. **Test Data Export**
- [ ] Export tanpa filter (semua data)
- [ ] Export dengan filter tanggal
- [ ] Export dengan data kosong
- [ ] Export dengan data lengkap

### 2. **Test Format**
- [ ] Layout tabel sesuai spesifikasi
- [ ] Kolom width proporsional
- [ ] Font size dan alignment benar
- [ ] Format tanggal konsisten

### 3. **Test Data Handling**
- [ ] Data kosong ditampilkan sebagai "-"
- [ ] Tanggal surat optional berfungsi
- [ ] Nomor urut dari database
- [ ] Text wrapping untuk data panjang

## Catatan Penting

1. **Data Source**: Menggunakan variabel `$ekspedisi` yang dikirim dari controller
2. **Responsive**: Layout disesuaikan untuk A4 landscape
3. **Compatibility**: Mendukung filter tanggal dari halaman index
4. **Performance**: Efficient looping untuk data besar
5. **Styling**: Konsisten dengan format PDF lainnya di sistem

## Integrasi dengan Controller

Controller `Ekspedisi::export()` sudah menyediakan:
- Data `$ekspedisi` dengan join ke tabel users
- Support filter tanggal via GET parameters
- User information untuk header PDF
- Statistics data (jika diperlukan)

Format data yang diterima view:
```php
$data = [
    'ekspedisi' => $ekspedisi_data,
    'user' => $user_info,
    'statistics' => $statistics_data
];
```
